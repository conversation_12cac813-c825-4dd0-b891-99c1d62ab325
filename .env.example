# MonadFaas Environment Variables
# Copy this file to .env and fill in your values

# Your private key for Monad account
# Get this from MetaMask: Account Details → Export Private Key
PRIVATE_KEY=0xYourPrivateKeyHere

# Optional: Etherscan API key for contract verification
# Get from https://etherscan.io/apis
ETHERSCAN_API_KEY=YourEtherscanApiKeyHere

# Network Configuration (already set in code)
# MONAD_RPC_URL=https://testnet-rpc.monad.xyz
# MONAD_MAINNET_RPC_URL=https://rpc.monad.xyz
# ACCOUNT_ADDRESS=0xYourAccountAddress

# Optional: Custom gas settings
# GAS_PRICE=50
# MAX_FEE_PER_GAS=100
# MAX_PRIORITY_FEE_PER_GAS=50
