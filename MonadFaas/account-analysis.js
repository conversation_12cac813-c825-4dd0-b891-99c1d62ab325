const { ethers } = require('ethers');

async function analyzeAccount() {
    const provider = new ethers.JsonRpcProvider('http://localhost:8545');
    
    // The account paying for gas
    const payingAccount = '******************************************';
    const privateKey = '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80';
    
    console.log('💰 MonadFaas Gas Fee Analysis');
    console.log('='.repeat(50));
    console.log();
    
    // Current balance
    const currentBalance = await provider.getBalance(payingAccount);
    const startingBalance = ethers.parseEther('10000'); // Anvil starts with 10,000 ETH
    const totalSpent = startingBalance - currentBalance;
    
    console.log('🏦 Account Information:');
    console.log(`   Address: ${payingAccount}`);
    console.log(`   Private Key: ${privateKey}`);
    console.log(`   Starting Balance: ${ethers.formatEther(startingBalance)} ETH`);
    console.log(`   Current Balance: ${ethers.formatEther(currentBalance)} ETH`);
    console.log(`   Total Spent: ${ethers.formatEther(totalSpent)} ETH`);
    console.log();
    
    // Get recent transactions
    const currentBlock = await provider.getBlockNumber();
    console.log(`📊 Transaction Analysis (Current Block: ${currentBlock}):`);
    console.log();
    
    let totalGasUsed = 0;
    let totalGasCost = 0;
    let transactionCount = 0;
    
    // Analyze recent blocks for transactions from our account
    for (let i = Math.max(1, currentBlock - 50); i <= currentBlock; i++) {
        try {
            const block = await provider.getBlock(i, true);
            if (block && block.transactions) {
                for (const tx of block.transactions) {
                    if (tx.from && tx.from.toLowerCase() === payingAccount.toLowerCase()) {
                        const receipt = await provider.getTransactionReceipt(tx.hash);
                        if (receipt) {
                            const gasCost = receipt.gasUsed * receipt.gasPrice;
                            totalGasUsed += Number(receipt.gasUsed);
                            totalGasCost += Number(gasCost);
                            transactionCount++;
                            
                            // Determine transaction type
                            let txType = 'Unknown';
                            if (tx.to === '0x5FbDB2315678afecb367f032d93F642f64180aa3') {
                                txType = 'Original Contract';
                            } else if (tx.to === '0x58d0d610674C69F27B7519a6e2746E8b814548DE') {
                                txType = 'Optimized Contract';
                            } else if (!tx.to) {
                                txType = 'Contract Deployment';
                            }
                            
                            console.log(`   Block ${i}: ${txType}`);
                            console.log(`   ├── Hash: ${tx.hash.slice(0, 10)}...`);
                            console.log(`   ├── Gas Used: ${receipt.gasUsed.toLocaleString()}`);
                            console.log(`   ├── Gas Price: ${ethers.formatUnits(receipt.gasPrice, 'gwei')} gwei`);
                            console.log(`   └── Cost: ${ethers.formatEther(gasCost)} ETH`);
                            console.log();
                        }
                    }
                }
            }
        } catch (error) {
            // Skip blocks that might not exist
        }
    }
    
    console.log('📈 Summary:');
    console.log(`   Total Transactions: ${transactionCount}`);
    console.log(`   Total Gas Used: ${totalGasUsed.toLocaleString()}`);
    console.log(`   Total Gas Cost: ${ethers.formatEther(totalGasCost)} ETH`);
    console.log(`   Average Gas per TX: ${Math.round(totalGasUsed / Math.max(transactionCount, 1)).toLocaleString()}`);
    console.log();
    
    // Show all Anvil accounts for reference
    console.log('🔑 All Available Anvil Test Accounts:');
    const testAccounts = [
        '******************************************', // Account 0 (currently used)
        '******************************************', // Account 1
        '******************************************', // Account 2
        '******************************************', // Account 3
        '******************************************'  // Account 4
    ];
    
    for (let i = 0; i < testAccounts.length; i++) {
        const balance = await provider.getBalance(testAccounts[i]);
        const isCurrentAccount = testAccounts[i].toLowerCase() === payingAccount.toLowerCase();
        const marker = isCurrentAccount ? '👈 PAYING ACCOUNT' : '';
        console.log(`   Account ${i}: ${testAccounts[i]} - ${ethers.formatEther(balance)} ETH ${marker}`);
    }
    console.log();
    
    // Real-world context
    console.log('🌍 Real-World Context:');
    console.log('   On Monad Mainnet:');
    console.log('   ├── This would be deducted from your actual MON balance');
    console.log('   ├── You would need MON tokens to pay for gas');
    console.log('   └── Gas prices would be much lower than Ethereum');
    console.log();
    console.log('   On Ethereum Mainnet:');
    console.log('   ├── This would cost real ETH');
    console.log(`   ├── At current prices: ~$${(parseFloat(ethers.formatEther(totalGasCost)) * 3000).toFixed(2)} USD`);
    console.log('   └── Our optimizations save significant money!');
    console.log();
    
    console.log('💡 Key Points:');
    console.log('   • Currently using Anvil testnet (fake ETH)');
    console.log('   • Account 0 is paying for all transactions');
    console.log('   • Started with 10,000 ETH, still has plenty left');
    console.log('   • On real networks, you\'d need real tokens');
    console.log('   • Gas optimizations save real money in production');
}

analyzeAccount().catch(console.error);
