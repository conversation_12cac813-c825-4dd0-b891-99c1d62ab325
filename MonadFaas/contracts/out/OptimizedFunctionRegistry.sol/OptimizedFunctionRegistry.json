{"abi": [{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEVELOPER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "MAX_GAS_LIMIT", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addTrigger", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "batchRegisterFunctions", "inputs": [{"name": "names", "type": "string[]", "internalType": "string[]"}, {"name": "descriptions", "type": "string[]", "internalType": "string[]"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes32[]", "internalType": "bytes32[]"}, {"name": "gasLimits", "type": "uint96[]", "internalType": "uint96[]"}, {"name": "runtimes", "type": "string[]", "internalType": "string[]"}], "outputs": [{"name": "functionIds", "type": "uint256[]", "internalType": "uint256[]"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "executionHistory", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "timestamp", "type": "uint64", "internalType": "uint64"}, {"name": "gasUsed", "type": "uint32", "internalType": "uint32"}, {"name": "success", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "fireTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "functions", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "createdAt", "type": "uint64", "internalType": "uint64"}, {"name": "executionCount", "type": "uint64", "internalType": "uint64"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "runtime", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getExecutionCount", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint64", "internalType": "uint64"}], "stateMutability": "view"}, {"type": "function", "name": "getFunction", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct OptimizedFunctionRegistry.FunctionMetadata", "components": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "createdAt", "type": "uint64", "internalType": "uint64"}, {"name": "executionCount", "type": "uint64", "internalType": "uint64"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "runtime", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct OptimizedFunctionRegistry.TriggerRule", "components": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "lastTriggered", "type": "uint64", "internalType": "uint64"}, {"name": "triggerCount", "type": "uint64", "internalType": "uint64"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}]}], "stateMutability": "view"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "isActive", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "nextFunctionId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nextTriggerId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "registerFunction", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "gasLimit", "type": "uint96", "internalType": "uint96"}, {"name": "runtime", "type": "string", "internalType": "string"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reportExecution", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "success", "type": "bool", "internalType": "bool"}, {"name": "", "type": "bytes", "internalType": "bytes"}, {"name": "gasUsed", "type": "uint32", "internalType": "uint32"}, {"name": "", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "triggers", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "lastTriggered", "type": "uint64", "internalType": "uint64"}, {"name": "triggerCount", "type": "uint64", "internalType": "uint64"}, {"name": "triggerType", "type": "uint8", "internalType": "enum OptimizedFunctionRegistry.TriggerType"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}], "stateMutability": "view"}, {"type": "event", "name": "FunctionExecuted", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "success", "type": "bool", "indexed": false, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "FunctionRegistered", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "wasmHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TriggerAdded", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "TriggerFired", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "FunctionInactive", "inputs": []}, {"type": "error", "name": "FunctionNotFound", "inputs": []}, {"type": "error", "name": "GasLimitExceeded", "inputs": []}, {"type": "error", "name": "InvalidWasmHash", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "TriggerInactive", "inputs": []}, {"type": "error", "name": "TriggerNotFound", "inputs": []}, {"type": "error", "name": "UnauthorizedAccess", "inputs": []}], "bytecode": {"object": "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", "sourceMap": "293:9174:24:-:0;;;2369:1;2337:33;;2407:1;2376:32;;3278:165;;;;;;;;;-1:-1:-1;1857:1:18;2061:21;;3302:42:24;2232:4:15;3333:10:24;3302;:42::i;:::-;-1:-1:-1;3354:34:24;405:23;3377:10;3354;:34::i;:::-;-1:-1:-1;3398:38:24;475:27;3425:10;3398;:38::i;:::-;;293:9174;;6179:316:15;6256:4;2954:12;;;;;;;;;;;-1:-1:-1;;;;;2954:29:15;;;;;;;;;;;;6272:217;;6315:6;:12;;;;;;;;;;;-1:-1:-1;;;;;6315:29:15;;;;;;;;;:36;;-1:-1:-1;;6315:36:15;6347:4;6315:36;;;6397:12;735:10:17;;656:96;6397:12:15;-1:-1:-1;;;;;6370:40:15;6388:7;-1:-1:-1;;;;;6370:40:15;6382:4;6370:40;;;;;;;;;;-1:-1:-1;6431:4:15;6424:11;;6272:217;-1:-1:-1;6473:5:15;6272:217;6179:316;;;;:::o;293:9174:24:-;;;;;;;", "linkReferences": {}}, "deployedBytecode": {"object": "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", "sourceMap": "293:9174:24:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2265:61;;;;;;:::i;:::-;;:::i;:::-;;;;-1:-1:-1;;;;;680:31:25;;;662:50;;760:10;748:23;;;743:2;728:18;;721:51;815:14;808:22;788:18;;;781:50;650:2;635:18;2265:61:24;;;;;;;;2565:202:15;;;;;;:::i;:::-;;:::i;:::-;;;1298:14:25;;1291:22;1273:41;;1261:2;1246:18;2565:202:15;1133:187:25;9066:170:24;;;;;;:::i;:::-;9163:6;9193:21;;;:9;:21;;;;;;;;:36;;-1:-1:-1;;;9193:36:24;;-1:-1:-1;;;;;9193:36:24;;9066:170;;;;-1:-1:-1;;;;;1672:31:25;;;1654:50;;1642:2;1627:18;9066:170:24;1510:200:25;2337:33:24;;;;;;;;;1861:25:25;;;1849:2;1834:18;2337:33:24;1715:177:25;8601:166:24;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;3810:120:15:-;;;;;;:::i;:::-;3875:7;3901:12;;;;;;;;;;:22;;;;3810:120;4226:136;;;;;;:::i;:::-;;:::i;:::-;;5328:245;;;;;;:::i;:::-;;:::i;2376:32:24:-;;;;;;5406:694;;;;;;:::i;:::-;;:::i;7029:1500::-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;4498:843::-;;;;;;:::i;:::-;;:::i;368:60::-;;-1:-1:-1;;;;;;;;;;;368:60:24;;9314:151;;;;;;:::i;:::-;9402:4;9430:21;;;:9;:21;;;;;;;;:28;;-1:-1:-1;;;9430:28:24;;;;;9314:151;434:68;;475:27;434:68;;2854:136:15;;;;;;:::i;:::-;;:::i;8834:157:24:-;;;;;;:::i;:::-;;:::i;:::-;;;;;;;:::i;2187:49:15:-;;2232:4;2187:49;;2212:47:24;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;:::i;6176:775::-;;;;;;:::i;:::-;;:::i;3528:901::-;;;;;;:::i;:::-;;:::i;4642:138:15:-;;;;;;:::i;:::-;;:::i;2414:49:24:-;;2454:9;2414:49;;2153:53;;;;;;:::i;:::-;;:::i;:::-;;;;;;;;;;;;;;;:::i;2265:61::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2265:61:24;;;-1:-1:-1;;;;2265:61:24;;;;;-1:-1:-1;;;;2265:61:24;;;;;:::o;2565:202:15:-;2650:4;-1:-1:-1;;;;;;2673:47:15;;-1:-1:-1;;;2673:47:15;;:87;;-1:-1:-1;;;;;;;;;;862:40:19;;;2724:36:15;2666:94;2565:202;-1:-1:-1;;2565:202:15:o;8601:166:24:-;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8739:21:24;;;;:9;:21;;;;;;;;;8732:28;;;;;;;;;;;;;;;-1:-1:-1;;;;;8732:28:24;;;;;;;;;-1:-1:-1;;;8732:28:24;;;-1:-1:-1;;;;;8732:28:24;;;;;;;;;;;;-1:-1:-1;;;;;8732:28:24;;;;;;;-1:-1:-1;;;8732:28:24;;;;;;;-1:-1:-1;;;8732:28:24;;;;;;;;;;;;;;;;;8739:21;8732:28;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8601:166;;;:::o;4226:136:15:-;3875:7;3901:12;;;;;;;;;;:22;;;2464:16;2475:4;2464:10;:16::i;:::-;4330:25:::1;4341:4;4347:7;4330:10;:25::i;:::-;;4226:136:::0;;;:::o;5328:245::-;-1:-1:-1;;;;;5421:34:15;;735:10:17;5421:34:15;5417:102;;5478:30;;-1:-1:-1;;;5478:30:15;;;;;;;;;;;5417:102;5529:37;5541:4;5547:18;5529:11;:37::i;:::-;;5328:245;;:::o;5406:694:24:-;-1:-1:-1;;;;;;;;;;;2464:16:15;2475:4;2464:10;:16::i;:::-;5542:27:24::1;5572:19:::0;;;:8:::1;:19;::::0;;;;5605:18;;5572:19;;5605:23;5601:53:::1;;5637:17;;-1:-1:-1::0;;;5637:17:24::1;;;;;;;;;;;5601:53;5669:14;::::0;::::1;::::0;-1:-1:-1;;;5669:14:24;::::1;;;5664:45;;5692:17;;-1:-1:-1::0;;;5692:17:24::1;;;;;;;;;;;5664:45;5762:18:::0;;5720:29:::1;5752::::0;;;:9:::1;:29;::::0;;;;;;;5796:11;;::::1;::::0;-1:-1:-1;;;5796:11:24;::::1;;;5791:43;;5816:18;;-1:-1:-1::0;;;5816:18:24::1;;;;;;;;;;;5791:43;5941:21;::::0;;::::1;:47:::0;;-1:-1:-1;;;;;;;;5972:15:24::1;5941:47:::0;::::1;-1:-1:-1::0;;5941:47:24;::::1;::::0;::::1;6002:22:::0;;::::1;::::0;::::1;::::0;;::::1;;;-1:-1:-1::0;;6002:22:24;;;;;;;;;::::1;::::0;;6074:18;;6050:43:::1;::::0;6063:9;;6050:43:::1;::::0;5941:21:::1;::::0;6050:43:::1;5532:568;;5406:694:::0;;;;:::o;7029:1500::-;7270:28;7327:5;7357:29;;;:60;;;;-1:-1:-1;7390:27:24;;;7357:60;:106;;;;-1:-1:-1;7437:26:24;;;7357:106;:135;;;;-1:-1:-1;7467:25:24;;;7357:135;7349:169;;;;-1:-1:-1;;;7349:169:24;;15151:2:25;7349:169:24;;;15133:21:25;15190:2;15170:18;;;15163:30;-1:-1:-1;;;15209:18:25;;;15202:51;15270:18;;7349:169:24;;;;;;;;;7557:6;-1:-1:-1;;;;;7543:21:24;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;7543:21:24;;7529:35;;7580:9;7575:948;7599:6;7595:1;:10;7575:948;;;7714:1;7689:10;;7700:1;7689:13;;;;;;;:::i;:::-;;;;;;;:27;7685:57;;7725:17;;-1:-1:-1;;;7725:17:24;;;;;;;;;;;7685:57;2454:9;7760;;7770:1;7760:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;-1:-1:-1;;;;;7760:28:24;;7756:59;;;7797:18;;-1:-1:-1;;;7797:18:24;;;;;;;;;;;7756:59;7851:14;:16;;7830:18;;;7851:16;;;:::i;:::-;;;;;7830:37;;7898:10;7881:11;7893:1;7881:14;;;;;;;;:::i;:::-;;;;;;;;;;;:27;;;;7983:29;8015:21;;;:9;:21;;;;;;8066:10;;8077:1;8066:13;;;;;;;:::i;:::-;;;;;;;;8050:29;;-1:-1:-1;8105:5:24;;8111:1;8105:8;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;8093:9;;;;:20;;;:9;:20;:::i;:::-;;8146:12;;8159:1;8146:15;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;8127:16;;;;:34;;;:16;:34;:::i;:::-;-1:-1:-1;8175:10:24;;;:23;;-1:-1:-1;;;;;;8175:23:24;8188:10;8175:23;;;8228:9;;8238:1;8228:12;;;;;;;:::i;:::-;;;;;;;;;;;;;;:::i;:::-;8212:13;;;:28;;-1:-1:-1;;;;;8212:28:24;-1:-1:-1;;;;;;;;8212:28:24;;;;;;;;;;;;;;8254:11;;;:18;;-1:-1:-1;;;;;8286:40:24;;;8310:15;-1:-1:-1;;;;;8286:40:24;;;-1:-1:-1;;;;8340:23:24;;;8392:8;;8401:1;8392:11;;;;;;;:::i;:::-;;;;;;;;;;;;:::i;:::-;8377:12;;;;:26;;;:12;:26;:::i;:::-;-1:-1:-1;8454:10:24;8442;8423:57;8466:10;;8477:1;8466:13;;;;;;;:::i;:::-;;;;;;;8423:57;;;;1861:25:25;;1849:2;1834:18;;1715:177;8423:57:24;;;;;;;;-1:-1:-1;;8507:3:24;;7575:948;;;;7300:1229;7029:1500;;;;;;;;;;;;:::o;4498:843::-;4639:17;4700:21;;;:9;:21;;;;;4735:10;;;;-1:-1:-1;;;;;4735:10:24;4731:55;;4768:18;;-1:-1:-1;;;4768:18:24;;;;;;;;;;;4731:55;4800:10;;;;-1:-1:-1;;;;;4800:10:24;4814;4800:24;;;;:60;;;4829:31;-1:-1:-1;;;;;;;;;;;4849:10:24;4829:7;:31::i;:::-;4828:32;4800:60;4796:118;;;4883:20;;-1:-1:-1;;;4883:20:24;;;;;;;;;;;4796:118;4936:13;:15;;;:13;:15;;;:::i;:::-;;;;-1:-1:-1;5007:27:24;5037:19;;;:8;:19;;;;;5066:31;;;5107:19;;;:33;;4924:27;;-1:-1:-1;5037:19:24;;5129:11;;5107:19;-1:-1:-1;;;;5107:33:24;-1:-1:-1;;;5129:11:24;5107:33;;;;;;;;:::i;:::-;;;;;-1:-1:-1;5150:19:24;;;:33;5172:11;;5150:19;:33;:::i;:::-;-1:-1:-1;5210:4:24;5193:14;;:21;;-1:-1:-1;;5259:24:24;-1:-1:-1;;;5259:24:24;;;5299:35;;5323:10;;5312:9;;5299:35;;5193:21;;5299:35;4658:683;;4498:843;;;;;;:::o;2854:136:15:-;2931:4;2954:12;;;;;;;;;;;-1:-1:-1;;;;;2954:29:15;;;;;;;;;;;;;;;2854:136::o;8834:157:24:-;8923:18;-1:-1:-1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8923:18:24;8965:19;;;;:8;:19;;;;;;;;;8958:26;;;;;;;;;;;;;;;-1:-1:-1;;;;;8958:26:24;;;;;;;;;;-1:-1:-1;;;8958:26:24;;;;;;;;;;;;8965:19;8958:26;;;;-1:-1:-1;;;8958:26:24;;;;;;;;;;;;:::i;:::-;;;;;;;;;:::i;:::-;;;;;;;-1:-1:-1;;;8958:26:24;;;;;;;;;;;;;;;;;;;;;;;:::i;2212:47::-;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2212:47:24;;;;-1:-1:-1;;;2212:47:24;;;;;;;-1:-1:-1;;;2212:47:24;;;;;-1:-1:-1;;;2212:47:24;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;6176:775::-;-1:-1:-1;;;;;;;;;;;2464:16:15;2475:4;2464:10;:16::i;:::-;6433:29:24::1;6465:21:::0;;;:9:::1;:21;::::0;;;;6500:10:::1;::::0;::::1;::::0;-1:-1:-1;;;;;6500:10:24::1;6496:55;;6533:18;;-1:-1:-1::0;;;6533:18:24::1;;;;;;;;;;;6496:55;6628:4;:19;;;:21;;;;;;;;;-1:-1:-1::0;;;;;6628:21:24::1;;;;;;;;;;;;;;-1:-1:-1::0;;;;;6628:21:24::1;;;;;-1:-1:-1::0;;;;;6628:21:24::1;;;;;;;6710:16;:28;6727:10;6710:28;;;;;;;;;;;6744:135;;;;;;;;6792:15;-1:-1:-1::0;;;;;6744:135:24::1;;;;;6831:7;6744:135;;;;;;6861:7;6744:135;;;;::::0;6710:170:::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1::0;;;;;6710:170:24::1;;;;;-1:-1:-1::0;;;;;6710:170:24::1;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6925:9;6913:10;6896:48;6936:7;6896:48;;;;1298:14:25::0;1291:22;1273:41;;1261:2;1246:18;;1133:187;6896:48:24::1;;;;;;;;6423:528;6176:775:::0;;;;;;;;;:::o;3528:901::-;3729:18;3763:8;3759:52;;3794:17;;-1:-1:-1;;;3794:17:24;;;;;;;;;;;3759:52;2454:9;3825:8;-1:-1:-1;;;;;3825:24:24;;3821:55;;;3858:18;;-1:-1:-1;;;3858:18:24;;;;;;;;;;;3821:55;3900:14;:16;;;:14;:16;;;:::i;:::-;;;;-1:-1:-1;3991:29:24;4023:21;;;:9;:21;;;;;4054:24;;;3887:29;;-1:-1:-1;4088:9:24;;;:16;4100:4;;4088:9;:16;:::i;:::-;-1:-1:-1;4114:16:24;;;:30;4133:11;;4114:16;:30;:::i;:::-;-1:-1:-1;4167:10:24;-1:-1:-1;;;;;;;;4187:24:24;;;;4154:10;;;4187:24;4221:11;;;:18;;-1:-1:-1;;;;;4249:40:24;;;4273:15;-1:-1:-1;;;;;4249:40:24;;;-1:-1:-1;;;;4299:23:24;;;4332:12;;;:22;4347:7;;4332:12;:22;:::i;:::-;-1:-1:-1;4370:52:24;;1861:25:25;;;4401:10:24;;4389;;4370:52;;1849:2:25;1834:18;4370:52:24;;;;;;;3749:680;3528:901;;;;;;;;;;:::o;4642:138:15:-;3875:7;3901:12;;;;;;;;;;:22;;;2464:16;2475:4;2464:10;:16::i;:::-;4747:26:::1;4759:4;4765:7;4747:11;:26::i;2153:53:24:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;2153:53:24;;;-1:-1:-1;;;2153:53:24;;;-1:-1:-1;;;;;2153:53:24;;-1:-1:-1;;;;;2153:53:24;;;;-1:-1:-1;;;2153:53:24;;;;;;-1:-1:-1;;;2153:53:24;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::o;3199:103:15:-;3265:30;3276:4;735:10:17;3265::15;:30::i;:::-;3199:103;:::o;6179:316::-;6256:4;6277:22;6285:4;6291:7;6277;:22::i;:::-;6272:217;;6315:6;:12;;;;;;;;;;;-1:-1:-1;;;;;6315:29:15;;;;;;;;;:36;;-1:-1:-1;;6315:36:15;6347:4;6315:36;;;6397:12;735:10:17;;656:96;6397:12:15;-1:-1:-1;;;;;6370:40:15;6388:7;-1:-1:-1;;;;;6370:40:15;6382:4;6370:40;;;;;;;;;;-1:-1:-1;6431:4:15;6424:11;;6272:217;-1:-1:-1;6473:5:15;6466:12;;6732:317;6810:4;6830:22;6838:4;6844:7;6830;:22::i;:::-;6826:217;;;6900:5;6868:12;;;;;;;;;;;-1:-1:-1;;;;;6868:29:15;;;;;;;;;;:37;;-1:-1:-1;;6868:37:15;;;6924:40;735:10:17;;6868:12:15;;6924:40;;6900:5;6924:40;-1:-1:-1;6985:4:15;6978:11;;3432:197;3520:22;3528:4;3534:7;3520;:22::i;:::-;3515:108;;3565:47;;-1:-1:-1;;;3565:47:15;;-1:-1:-1;;;;;19975:32:25;;3565:47:15;;;19957:51:25;20024:18;;;20017:34;;;19930:18;;3565:47:15;19783:274:25;3515:108:15;3432:197;;:::o;14:248:25:-;82:6;90;143:2;131:9;122:7;118:23;114:32;111:52;;;159:1;156;149:12;111:52;-1:-1:-1;;182:23:25;;;252:2;237:18;;;224:32;;-1:-1:-1;14:248:25:o;842:286::-;900:6;953:2;941:9;932:7;928:23;924:32;921:52;;;969:1;966;959:12;921:52;995:23;;-1:-1:-1;;;;;;1047:32:25;;1037:43;;1027:71;;1094:1;1091;1084:12;1027:71;1117:5;842:286;-1:-1:-1;;;842:286:25:o;1325:180::-;1384:6;1437:2;1425:9;1416:7;1412:23;1408:32;1405:52;;;1453:1;1450;1443:12;1405:52;-1:-1:-1;1476:23:25;;1325:180;-1:-1:-1;1325:180:25:o;2121:423::-;2163:3;2201:5;2195:12;2228:6;2223:3;2216:19;2253:1;2263:162;2277:6;2274:1;2271:13;2263:162;;;2339:4;2395:13;;;2391:22;;2385:29;2367:11;;;2363:20;;2356:59;2292:12;2263:162;;;2267:3;2470:1;2463:4;2454:6;2449:3;2445:16;2441:27;2434:38;2533:4;2526:2;2522:7;2517:2;2509:6;2505:15;2501:29;2496:3;2492:39;2488:50;2481:57;;;2121:423;;;;:::o;2549:1442::-;2748:2;2737:9;2730:21;2793:6;2787:13;2782:2;2771:9;2767:18;2760:41;2711:4;2848:2;2840:6;2836:15;2830:22;2861:52;2909:2;2898:9;2894:18;2880:12;-1:-1:-1;;;;;1963:31:25;1951:44;;1897:104;2861:52;-1:-1:-1;2962:2:25;2950:15;;2944:22;-1:-1:-1;;;;;2071:38:25;;3024:2;3009:18;;2059:51;-1:-1:-1;3077:2:25;3065:15;;3059:22;-1:-1:-1;;;;;332:30:25;;3139:3;3124:19;;320:43;-1:-1:-1;3193:3:25;3181:16;;3175:23;-1:-1:-1;;;;;332:30:25;;3256:3;3241:19;;320:43;-1:-1:-1;3310:3:25;3298:16;;3292:23;444:13;;437:21;3371:3;3356:19;;425:34;3324:52;3425:3;3417:6;3413:16;3407:23;3449:6;3492:2;3486:3;3475:9;3471:19;3464:31;3518:54;3567:3;3556:9;3552:19;3536:14;3518:54;:::i;:::-;3504:68;;3621:3;3613:6;3609:16;3603:23;3649:2;3645:7;3671:3;3738:2;3726:9;3718:6;3714:22;3710:31;3705:2;3694:9;3690:18;3683:59;3765:41;3799:6;3783:14;3765:41;:::i;:::-;3843:15;;;3837:22;3899;;;3895:31;;;3875:18;;;3868:59;3751:55;-1:-1:-1;3837:22:25;-1:-1:-1;3944:41:25;3751:55;3837:22;3944:41;:::i;:::-;3936:49;2549:1442;-1:-1:-1;;;;;;2549:1442:25:o;4363:354::-;4431:6;4439;4492:2;4480:9;4471:7;4467:23;4463:32;4460:52;;;4508:1;4505;4498:12;4460:52;4531:23;;;-1:-1:-1;4604:2:25;4589:18;;4576:32;-1:-1:-1;;;;;4637:31:25;;4627:42;;4617:70;;4683:1;4680;4673:12;4617:70;4706:5;4696:15;;;4363:354;;;;;:::o;4722:347::-;4773:8;4783:6;4837:3;4830:4;4822:6;4818:17;4814:27;4804:55;;4855:1;4852;4845:12;4804:55;-1:-1:-1;4878:20:25;;-1:-1:-1;;;;;4910:30:25;;4907:50;;;4953:1;4950;4943:12;4907:50;4990:4;4982:6;4978:17;4966:29;;5042:3;5035:4;5026:6;5018;5014:19;5010:30;5007:39;5004:59;;;5059:1;5056;5049:12;5004:59;4722:347;;;;;:::o;5074:477::-;5153:6;5161;5169;5222:2;5210:9;5201:7;5197:23;5193:32;5190:52;;;5238:1;5235;5228:12;5190:52;5274:9;5261:23;5251:33;;5335:2;5324:9;5320:18;5307:32;-1:-1:-1;;;;;5354:6:25;5351:30;5348:50;;;5394:1;5391;5384:12;5348:50;5433:58;5483:7;5474:6;5463:9;5459:22;5433:58;:::i;:::-;5074:477;;5510:8;;-1:-1:-1;5407:84:25;;-1:-1:-1;;;;5074:477:25:o;5556:375::-;5627:8;5637:6;5691:3;5684:4;5676:6;5672:17;5668:27;5658:55;;5709:1;5706;5699:12;5658:55;-1:-1:-1;5732:20:25;;-1:-1:-1;;;;;5764:30:25;;5761:50;;;5807:1;5804;5797:12;5761:50;5844:4;5836:6;5832:17;5820:29;;5904:3;5897:4;5887:6;5884:1;5880:14;5872:6;5868:27;5864:38;5861:47;5858:67;;;5921:1;5918;5911:12;5936:1795;6201:6;6209;6217;6225;6233;6241;6249;6257;6265;6273;6326:3;6314:9;6305:7;6301:23;6297:33;6294:53;;;6343:1;6340;6333:12;6294:53;6383:9;6370:23;-1:-1:-1;;;;;6453:2:25;6445:6;6442:14;6439:34;;;6469:1;6466;6459:12;6439:34;6508:78;6578:7;6569:6;6558:9;6554:22;6508:78;:::i;:::-;6605:8;;-1:-1:-1;6482:104:25;-1:-1:-1;6693:2:25;6678:18;;6665:32;;-1:-1:-1;6709:16:25;;;6706:36;;;6738:1;6735;6728:12;6706:36;6777:80;6849:7;6838:8;6827:9;6823:24;6777:80;:::i;:::-;6876:8;;-1:-1:-1;6751:106:25;-1:-1:-1;6964:2:25;6949:18;;6936:32;;-1:-1:-1;6980:16:25;;;6977:36;;;7009:1;7006;6999:12;6977:36;7048:80;7120:7;7109:8;7098:9;7094:24;7048:80;:::i;:::-;7147:8;;-1:-1:-1;7022:106:25;-1:-1:-1;7235:2:25;7220:18;;7207:32;;-1:-1:-1;7251:16:25;;;7248:36;;;7280:1;7277;7270:12;7248:36;7319:80;7391:7;7380:8;7369:9;7365:24;7319:80;:::i;:::-;7418:8;;-1:-1:-1;7293:106:25;-1:-1:-1;7506:3:25;7491:19;;7478:33;;-1:-1:-1;7523:16:25;;;7520:36;;;7552:1;7549;7542:12;7520:36;;7591:80;7663:7;7652:8;7641:9;7637:24;7591:80;:::i;:::-;7565:106;;7690:8;7680:18;;;7717:8;7707:18;;;5936:1795;;;;;;;;;;;;;:::o;7736:632::-;7907:2;7959:21;;;8029:13;;7932:18;;;8051:22;;;7878:4;;7907:2;8130:15;;;;8104:2;8089:18;;;7878:4;8173:169;8187:6;8184:1;8181:13;8173:169;;;8248:13;;8236:26;;8317:15;;;;8282:12;;;;8209:1;8202:9;8173:169;;;-1:-1:-1;8359:3:25;;7736:632;-1:-1:-1;;;;;;7736:632:25:o;8373:638::-;8478:6;8486;8494;8502;8555:2;8543:9;8534:7;8530:23;8526:32;8523:52;;;8571:1;8568;8561:12;8523:52;8607:9;8594:23;8584:33;;8667:2;8656:9;8652:18;8639:32;8700:1;8693:5;8690:12;8680:40;;8716:1;8713;8706:12;8680:40;8739:5;-1:-1:-1;8795:2:25;8780:18;;8767:32;-1:-1:-1;;;;;8811:30:25;;8808:50;;;8854:1;8851;8844:12;8808:50;8893:58;8943:7;8934:6;8923:9;8919:22;8893:58;:::i;:::-;8373:638;;;;-1:-1:-1;8970:8:25;-1:-1:-1;;;;8373:638:25:o;9016:127::-;9077:10;9072:3;9068:20;9065:1;9058:31;9108:4;9105:1;9098:15;9132:4;9129:1;9122:15;9148:239;9231:1;9224:5;9221:12;9211:143;;9276:10;9271:3;9267:20;9264:1;9257:31;9311:4;9308:1;9301:15;9339:4;9336:1;9329:15;9211:143;9363:18;;9148:239::o;9392:833::-;9581:2;9570:9;9563:21;9626:6;9620:13;9615:2;9604:9;9600:18;9593:41;9544:4;9681:2;9673:6;9669:15;9663:22;-1:-1:-1;;;;;9776:2:25;9762:12;9758:21;9753:2;9742:9;9738:18;9731:49;9844:2;9838;9830:6;9826:15;9820:22;9816:31;9811:2;9800:9;9796:18;9789:59;;;9897:2;9889:6;9885:15;9879:22;9910:64;9969:3;9958:9;9954:19;9938:14;9910:64;:::i;:::-;;10043:3;10035:6;10031:16;10025:23;10018:31;10011:39;10005:3;9994:9;9990:19;9983:68;10100:3;10092:6;10088:16;10082:23;10143:4;10136;10125:9;10121:20;10114:34;10165:54;10214:3;10203:9;10199:19;10183:14;10165:54;:::i;:::-;10157:62;9392:833;-1:-1:-1;;;;9392:833:25:o;10230:674::-;10504:25;;;-1:-1:-1;;;;;10602:15:25;;;10597:2;10582:18;;10575:43;10654:15;;10649:2;10634:18;;10627:43;10485:4;10679:55;10730:2;10715:18;;10707:6;10679:55;:::i;:::-;10785:6;10778:14;10771:22;10765:3;10754:9;10750:19;10743:51;10831:3;10825;10814:9;10810:19;10803:32;10852:46;10893:3;10882:9;10878:19;10870:6;10852:46;:::i;:::-;10844:54;10230:674;-1:-1:-1;;;;;;;;10230:674:25:o;10909:1190::-;11032:6;11040;11048;11056;11064;11072;11080;11088;11141:3;11129:9;11120:7;11116:23;11112:33;11109:53;;;11158:1;11155;11148:12;11109:53;11194:9;11181:23;11171:33;;11251:2;11240:9;11236:18;11223:32;11213:42;;11305:2;11294:9;11290:18;11277:32;11352:5;11345:13;11338:21;11331:5;11328:32;11318:60;;11374:1;11371;11364:12;11318:60;11397:5;-1:-1:-1;11453:2:25;11438:18;;11425:32;-1:-1:-1;;;;;11506:14:25;;;11503:34;;;11533:1;11530;11523:12;11503:34;11572:58;11622:7;11613:6;11602:9;11598:22;11572:58;:::i;:::-;11649:8;;-1:-1:-1;11546:84:25;-1:-1:-1;11736:3:25;11721:19;;11708:33;;-1:-1:-1;11785:10:25;11772:24;;11760:37;;11750:65;;11811:1;11808;11801:12;11750:65;11834:7;;-1:-1:-1;11894:3:25;11879:19;;11866:33;;11911:16;;;11908:36;;;11940:1;11937;11930:12;11908:36;;11979:60;12031:7;12020:8;12009:9;12005:24;11979:60;:::i;:::-;10909:1190;;;;-1:-1:-1;10909:1190:25;;-1:-1:-1;10909:1190:25;;;;;;12058:8;-1:-1:-1;;;10909:1190:25:o;12104:179::-;12171:20;;-1:-1:-1;;;;;12220:38:25;;12210:49;;12200:77;;12273:1;12270;12263:12;12200:77;12104:179;;;:::o;12288:1149::-;12418:6;12426;12434;12442;12450;12458;12466;12474;12527:3;12515:9;12506:7;12502:23;12498:33;12495:53;;;12544:1;12541;12534:12;12495:53;12584:9;12571:23;-1:-1:-1;;;;;12654:2:25;12646:6;12643:14;12640:34;;;12670:1;12667;12660:12;12640:34;12709:58;12759:7;12750:6;12739:9;12735:22;12709:58;:::i;:::-;12786:8;;-1:-1:-1;12683:84:25;-1:-1:-1;12874:2:25;12859:18;;12846:32;;-1:-1:-1;12890:16:25;;;12887:36;;;12919:1;12916;12909:12;12887:36;12958:60;13010:7;12999:8;12988:9;12984:24;12958:60;:::i;:::-;13037:8;;-1:-1:-1;12932:86:25;-1:-1:-1;13119:2:25;13104:18;;13091:32;;-1:-1:-1;12932:86:25;;-1:-1:-1;13142:37:25;13175:2;13160:18;;13142:37;:::i;:::-;13132:47;;13232:3;13221:9;13217:19;13204:33;13188:49;;13262:2;13252:8;13249:16;13246:36;;;13278:1;13275;13268:12;13442:1117;13847:25;;;-1:-1:-1;;;;;13908:32:25;;13903:2;13888:18;;13881:60;-1:-1:-1;;;;;13977:39:25;;13972:2;13957:18;;13950:67;-1:-1:-1;;;;;14090:15:25;;;14085:2;14070:18;;14063:43;14143:15;;14137:3;14122:19;;14115:44;14203:14;;14196:22;13928:3;14175:19;;14168:51;13835:3;14250;14235:19;;14228:31;;;13806:4;;14282:45;14308:18;;;14300:6;14282:45;:::i;:::-;14268:59;;14376:9;14368:6;14364:22;14358:3;14347:9;14343:19;14336:51;14410:33;14436:6;14428;14410:33;:::i;:::-;14396:47;;14492:9;14484:6;14480:22;14474:3;14463:9;14459:19;14452:51;14520:33;14546:6;14538;14520:33;:::i;:::-;14512:41;13442:1117;-1:-1:-1;;;;;;;;;;;;13442:1117:25:o;14564:380::-;14643:1;14639:12;;;;14686;;;14707:61;;14761:4;14753:6;14749:17;14739:27;;14707:61;14814:2;14806:6;14803:14;14783:18;14780:38;14777:161;;14860:10;14855:3;14851:20;14848:1;14841:31;14895:4;14892:1;14885:15;14923:4;14920:1;14913:15;14777:161;;14564:380;;;:::o;15299:127::-;15360:10;15355:3;15351:20;15348:1;15341:31;15391:4;15388:1;15381:15;15415:4;15412:1;15405:15;15431:127;15492:10;15487:3;15483:20;15480:1;15473:31;15523:4;15520:1;15513:15;15547:4;15544:1;15537:15;15563:184;15621:6;15674:2;15662:9;15653:7;15649:23;15645:32;15642:52;;;15690:1;15687;15680:12;15642:52;15713:28;15731:9;15713:28;:::i;15752:232::-;15791:3;15812:17;;;15809:140;;15871:10;15866:3;15862:20;15859:1;15852:31;15906:4;15903:1;15896:15;15934:4;15931:1;15924:15;15809:140;-1:-1:-1;15976:1:25;15965:13;;15752:232::o;15989:522::-;16067:4;16073:6;16133:11;16120:25;16227:2;16223:7;16212:8;16196:14;16192:29;16188:43;16168:18;16164:68;16154:96;;16246:1;16243;16236:12;16154:96;16273:33;;16325:20;;;-1:-1:-1;;;;;;16357:30:25;;16354:50;;;16400:1;16397;16390:12;16354:50;16433:4;16421:17;;-1:-1:-1;16464:14:25;16460:27;;;16450:38;;16447:58;;;16501:1;16498;16491:12;16642:545;16744:2;16739:3;16736:11;16733:448;;;16780:1;16805:5;16801:2;16794:17;16850:4;16846:2;16836:19;16920:2;16908:10;16904:19;16901:1;16897:27;16891:4;16887:38;16956:4;16944:10;16941:20;16938:47;;;-1:-1:-1;16979:4:25;16938:47;17034:2;17029:3;17025:12;17022:1;17018:20;17012:4;17008:31;16998:41;;17089:82;17107:2;17100:5;17097:13;17089:82;;;17152:17;;;17133:1;17122:13;17089:82;;;17093:3;;;16642:545;;;:::o;17363:1206::-;-1:-1:-1;;;;;17482:3:25;17479:27;17476:53;;;17509:18;;:::i;:::-;17538:94;17628:3;17588:38;17620:4;17614:11;17588:38;:::i;:::-;17582:4;17538:94;:::i;:::-;17658:1;17683:2;17678:3;17675:11;17700:1;17695:616;;;;18355:1;18372:3;18369:93;;;-1:-1:-1;18428:19:25;;;18415:33;18369:93;-1:-1:-1;;17320:1:25;17316:11;;;17312:24;17308:29;17298:40;17344:1;17340:11;;;17295:57;18475:78;;17668:895;;17695:616;16589:1;16582:14;;;16626:4;16613:18;;-1:-1:-1;;17731:17:25;;;17832:9;17854:229;17868:7;17865:1;17862:14;17854:229;;;17957:19;;;17944:33;17929:49;;18064:4;18049:20;;;;18017:1;18005:14;;;;17884:12;17854:229;;;17858:3;18111;18102:7;18099:16;18096:159;;;18235:1;18231:6;18225:3;18219;18216:1;18212:11;18208:21;18204:34;18200:39;18187:9;18182:3;18178:19;18165:33;18161:79;18153:6;18146:95;18096:159;;;18298:1;18292:3;18289:1;18285:11;18281:19;18275:4;18268:33;17668:895;;;17363:1206;;;:::o", "linkReferences": {}}, "methodIdentifiers": {"ADMIN_ROLE()": "75b238fc", "DEFAULT_ADMIN_ROLE()": "a217fddf", "DEVELOPER_ROLE()": "9103a0e0", "MAX_GAS_LIMIT()": "e3f5aa51", "addTrigger(uint256,uint8,bytes)": "711b0802", "batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])": "5f0710a2", "executionHistory(uint256,uint256)": "00d5c659", "fireTrigger(uint256,bytes)": "53e7768b", "functions(uint256)": "eac0bd90", "getExecutionCount(uint256)": "0250fc7f", "getFunction(uint256)": "0e097de7", "getRoleAdmin(bytes32)": "248a9ca3", "getTrigger(uint256)": "9c50472b", "grantRole(bytes32,address)": "2f2ff15d", "hasRole(bytes32,address)": "91d14854", "isActive(uint256)": "82afd23b", "nextFunctionId()": "02b9db7b", "nextTriggerId()": "42227fa4", "registerFunction(string,string,bytes32,uint96,string)": "b4332d86", "renounceRole(bytes32,address)": "36568abe", "reportExecution(uint256,uint256,bool,bytes,uint32,string)": "aed5a3e1", "revokeRole(bytes32,address)": "d547741f", "supportsInterface(bytes4)": "01ffc9a7", "triggers(uint256)": "a78dd42e"}, "rawMetadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"AccessControlBadConfirmation\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"internalType\":\"bytes32\",\"name\":\"neededRole\",\"type\":\"bytes32\"}],\"name\":\"AccessControlUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FunctionInactive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"FunctionNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"GasLimitExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidWasmHash\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TriggerInactive\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"TriggerNotFound\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"UnauthorizedAccess\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"name\":\"FunctionExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"}],\"name\":\"FunctionRegistered\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"previousAdminRole\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"newAdminRole\",\"type\":\"bytes32\"}],\"name\":\"RoleAdminChanged\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleGranted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"}],\"name\":\"RoleRevoked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"TriggerAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"TriggerFired\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEFAULT_ADMIN_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"DEVELOPER_ROLE\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_GAS_LIMIT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"name\":\"addTrigger\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string[]\",\"name\":\"names\",\"type\":\"string[]\"},{\"internalType\":\"string[]\",\"name\":\"descriptions\",\"type\":\"string[]\"},{\"internalType\":\"bytes32[]\",\"name\":\"wasmHashes\",\"type\":\"bytes32[]\"},{\"internalType\":\"uint96[]\",\"name\":\"gasLimits\",\"type\":\"uint96[]\"},{\"internalType\":\"string[]\",\"name\":\"runtimes\",\"type\":\"string[]\"}],\"name\":\"batchRegisterFunctions\",\"outputs\":[{\"internalType\":\"uint256[]\",\"name\":\"functionIds\",\"type\":\"uint256[]\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"executionHistory\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"timestamp\",\"type\":\"uint64\"},{\"internalType\":\"uint32\",\"name\":\"gasUsed\",\"type\":\"uint32\"},{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"}],\"name\":\"fireTrigger\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"functions\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"uint64\",\"name\":\"createdAt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"executionCount\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"getExecutionCount\",\"outputs\":[{\"internalType\":\"uint64\",\"name\":\"\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"getFunction\",\"outputs\":[{\"components\":[{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"uint64\",\"name\":\"createdAt\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"executionCount\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"internalType\":\"struct OptimizedFunctionRegistry.FunctionMetadata\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"}],\"name\":\"getRoleAdmin\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"}],\"name\":\"getTrigger\",\"outputs\":[{\"components\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"lastTriggered\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"triggerCount\",\"type\":\"uint64\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"internalType\":\"struct OptimizedFunctionRegistry.TriggerRule\",\"name\":\"\",\"type\":\"tuple\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"grantRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"hasRole\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"name\":\"isActive\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nextFunctionId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"nextTriggerId\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"string\",\"name\":\"name\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"description\",\"type\":\"string\"},{\"internalType\":\"bytes32\",\"name\":\"wasmHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint96\",\"name\":\"gasLimit\",\"type\":\"uint96\"},{\"internalType\":\"string\",\"name\":\"runtime\",\"type\":\"string\"}],\"name\":\"registerFunction\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"callerConfirmation\",\"type\":\"address\"}],\"name\":\"renounceRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"triggerId\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"success\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"\",\"type\":\"bytes\"},{\"internalType\":\"uint32\",\"name\":\"gasUsed\",\"type\":\"uint32\"},{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"name\":\"reportExecution\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"role\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"revokeRole\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes4\",\"name\":\"interfaceId\",\"type\":\"bytes4\"}],\"name\":\"supportsInterface\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"triggers\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"functionId\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"lastTriggered\",\"type\":\"uint64\"},{\"internalType\":\"uint64\",\"name\":\"triggerCount\",\"type\":\"uint64\"},{\"internalType\":\"enum OptimizedFunctionRegistry.TriggerType\",\"name\":\"triggerType\",\"type\":\"uint8\"},{\"internalType\":\"bool\",\"name\":\"active\",\"type\":\"bool\"},{\"internalType\":\"bytes\",\"name\":\"triggerData\",\"type\":\"bytes\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"author\":\"MonadBot\",\"details\":\"Gas-optimized Monad FaaS Function Registry\",\"errors\":{\"AccessControlBadConfirmation()\":[{\"details\":\"The caller of a function is not the expected one. NOTE: Don't confuse with {AccessControlUnauthorizedAccount}.\"}],\"AccessControlUnauthorizedAccount(address,bytes32)\":[{\"details\":\"The `account` is missing a role.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"RoleAdminChanged(bytes32,bytes32,bytes32)\":{\"details\":\"Emitted when `newAdminRole` is set as ``role``'s admin role, replacing `previousAdminRole` `DEFAULT_ADMIN_ROLE` is the starting admin for all roles, despite {RoleAdminChanged} not being emitted to signal this.\"},\"RoleGranted(bytes32,address,address)\":{\"details\":\"Emitted when `account` is granted `role`. `sender` is the account that originated the contract call. This account bears the admin role (for the granted role). Expected in cases where the role was granted using the internal {AccessControl-_grantRole}.\"},\"RoleRevoked(bytes32,address,address)\":{\"details\":\"Emitted when `account` is revoked `role`. `sender` is the account that originated the contract call:   - if using `revokeRole`, it is the admin role bearer   - if using `renounceRole`, it is the role bearer (i.e. `account`)\"}},\"kind\":\"dev\",\"methods\":{\"addTrigger(uint256,uint8,bytes)\":{\"details\":\"Add a trigger rule (gas optimized)\"},\"batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])\":{\"details\":\"Batch register functions for gas efficiency\"},\"fireTrigger(uint256,bytes)\":{\"details\":\"Fire a trigger (gas optimized)\"},\"getExecutionCount(uint256)\":{\"details\":\"Get execution count only (gas efficient)\"},\"getFunction(uint256)\":{\"details\":\"Get function metadata (view function)\"},\"getRoleAdmin(bytes32)\":{\"details\":\"Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}.\"},\"getTrigger(uint256)\":{\"details\":\"Get trigger rule (view function)\"},\"grantRole(bytes32,address)\":{\"details\":\"Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event.\"},\"hasRole(bytes32,address)\":{\"details\":\"Returns `true` if `account` has been granted `role`.\"},\"isActive(uint256)\":{\"details\":\"Check if function is active (gas efficient)\"},\"registerFunction(string,string,bytes32,uint96,string)\":{\"details\":\"Register a new serverless function (gas optimized)\"},\"renounceRole(bytes32,address)\":{\"details\":\"Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event.\"},\"reportExecution(uint256,uint256,bool,bytes,uint32,string)\":{\"details\":\"Report function execution (gas optimized)\"},\"revokeRole(bytes32,address)\":{\"details\":\"Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event.\"},\"supportsInterface(bytes4)\":{\"details\":\"See {IERC165-supportsInterface}.\"}},\"title\":\"OptimizedFunctionRegistry\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"src/OptimizedFunctionRegistry.sol\":\"OptimizedFunctionRegistry\"},\"evmVersion\":\"shanghai\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[\":@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/\",\":erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/\",\":forge-std/=lib/forge-std/src/\",\":halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/\",\":openzeppelin-contracts/=lib/openzeppelin-contracts/\"]},\"sources\":{\"lib/openzeppelin-contracts/contracts/access/AccessControl.sol\":{\"keccak256\":\"0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67\",\"dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx\"]},\"lib/openzeppelin-contracts/contracts/access/IAccessControl.sol\":{\"keccak256\":\"0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0\",\"dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf\"]},\"lib/openzeppelin-contracts/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol\":{\"keccak256\":\"0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287\",\"dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p\"]},\"lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol\":{\"keccak256\":\"0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621\",\"dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL\"]},\"src/OptimizedFunctionRegistry.sol\":{\"keccak256\":\"0x2a77c792714e62b41f5b80ee7f3a54851e16b5a0305bd78f10fd74365c88e17f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://d63ba57861c5e987655d79a6f77342c2797935a754c628d229670a4faf9e3526\",\"dweb:/ipfs/QmURcA1fjMPPVVhvo6p3cma84vxSMZPCNbmGXiessY4tBJ\"]}},\"version\":1}", "metadata": {"compiler": {"version": "0.8.20+commit.a1b79de6"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "type": "error", "name": "AccessControlBadConfirmation"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "type": "error", "name": "AccessControlUnauthorizedAccount"}, {"inputs": [], "type": "error", "name": "FunctionInactive"}, {"inputs": [], "type": "error", "name": "FunctionNotFound"}, {"inputs": [], "type": "error", "name": "GasLimitExceeded"}, {"inputs": [], "type": "error", "name": "InvalidWasmHash"}, {"inputs": [], "type": "error", "name": "ReentrancyGuardReentrantCall"}, {"inputs": [], "type": "error", "name": "TriggerInactive"}, {"inputs": [], "type": "error", "name": "TriggerNotFound"}, {"inputs": [], "type": "error", "name": "UnauthorizedAccess"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "bool", "name": "success", "type": "bool", "indexed": false}], "type": "event", "name": "FunctionExecuted", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}, {"internalType": "address", "name": "owner", "type": "address", "indexed": true}, {"internalType": "bytes32", "name": "wasmHash", "type": "bytes32", "indexed": false}], "type": "event", "name": "FunctionRegistered", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32", "indexed": true}, {"internalType": "bytes32", "name": "newAdminRole", "type": "bytes32", "indexed": true}], "type": "event", "name": "RoleAdminChanged", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleGranted", "anonymous": false}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32", "indexed": true}, {"internalType": "address", "name": "account", "type": "address", "indexed": true}, {"internalType": "address", "name": "sender", "type": "address", "indexed": true}], "type": "event", "name": "RoleRevoked", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}], "type": "event", "name": "TriggerAdded", "anonymous": false}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256", "indexed": true}, {"internalType": "uint256", "name": "functionId", "type": "uint256", "indexed": true}], "type": "event", "name": "TriggerFired", "anonymous": false}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "DEVELOPER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "MAX_GAS_LIMIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "addTrigger", "outputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}]}, {"inputs": [{"internalType": "string[]", "name": "names", "type": "string[]"}, {"internalType": "string[]", "name": "descriptions", "type": "string[]"}, {"internalType": "bytes32[]", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bytes32[]"}, {"internalType": "uint96[]", "name": "gasLimits", "type": "uint96[]"}, {"internalType": "string[]", "name": "runtimes", "type": "string[]"}], "stateMutability": "nonpayable", "type": "function", "name": "batchRegisterFunctions", "outputs": [{"internalType": "uint256[]", "name": "functionIds", "type": "uint256[]"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "executionHistory", "outputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint32", "name": "gasUsed", "type": "uint32"}, {"internalType": "bool", "name": "success", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function", "name": "fireTrigger"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "functions", "outputs": [{"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "uint64", "name": "createdAt", "type": "uint64"}, {"internalType": "uint64", "name": "executionCount", "type": "uint64"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "runtime", "type": "string"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getExecutionCount", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getFunction", "outputs": [{"internalType": "struct OptimizedFunctionRegistry.FunctionMetadata", "name": "", "type": "tuple", "components": [{"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "uint64", "name": "createdAt", "type": "uint64"}, {"internalType": "uint64", "name": "executionCount", "type": "uint64"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "runtime", "type": "string"}]}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "stateMutability": "view", "type": "function", "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}]}, {"inputs": [{"internalType": "uint256", "name": "triggerId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "getTrigger", "outputs": [{"internalType": "struct OptimizedFunctionRegistry.TriggerRule", "name": "", "type": "tuple", "components": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint64", "name": "lastTriggered", "type": "uint64"}, {"internalType": "uint64", "name": "triggerCount", "type": "uint64"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}]}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "grantRole"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "view", "type": "function", "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "isActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "nextFunctionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [], "stateMutability": "view", "type": "function", "name": "nextTriggerId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}]}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "bytes32", "name": "wasmHash", "type": "bytes32"}, {"internalType": "uint96", "name": "gasLimit", "type": "uint96"}, {"internalType": "string", "name": "runtime", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "registerFunction", "outputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}]}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "renounceRole"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint256", "name": "triggerId", "type": "uint256"}, {"internalType": "bool", "name": "success", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}, {"internalType": "uint32", "name": "gasUsed", "type": "uint32"}, {"internalType": "string", "name": "", "type": "string"}], "stateMutability": "nonpayable", "type": "function", "name": "reportExecution"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "stateMutability": "nonpayable", "type": "function", "name": "revokeRole"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "stateMutability": "view", "type": "function", "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}]}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function", "name": "triggers", "outputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "uint64", "name": "lastTriggered", "type": "uint64"}, {"internalType": "uint64", "name": "triggerCount", "type": "uint64"}, {"internalType": "enum OptimizedFunctionRegistry.TriggerType", "name": "triggerType", "type": "uint8"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bytes", "name": "triggerData", "type": "bytes"}]}], "devdoc": {"kind": "dev", "methods": {"addTrigger(uint256,uint8,bytes)": {"details": "Add a trigger rule (gas optimized)"}, "batchRegisterFunctions(string[],string[],bytes32[],uint96[],string[])": {"details": "Batch register functions for gas efficiency"}, "fireTrigger(uint256,bytes)": {"details": "Fire a trigger (gas optimized)"}, "getExecutionCount(uint256)": {"details": "Get execution count only (gas efficient)"}, "getFunction(uint256)": {"details": "Get function metadata (view function)"}, "getRoleAdmin(bytes32)": {"details": "Returns the admin role that controls `role`. See {grantRole} and {revokeRole}. To change a role's admin, use {_setRoleAdmin}."}, "getTrigger(uint256)": {"details": "Get trigger rule (view function)"}, "grantRole(bytes32,address)": {"details": "Grants `role` to `account`. If `account` had not been already granted `role`, emits a {RoleGranted} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleGranted} event."}, "hasRole(bytes32,address)": {"details": "Returns `true` if `account` has been granted `role`."}, "isActive(uint256)": {"details": "Check if function is active (gas efficient)"}, "registerFunction(string,string,bytes32,uint96,string)": {"details": "Register a new serverless function (gas optimized)"}, "renounceRole(bytes32,address)": {"details": "Revokes `role` from the calling account. Roles are often managed via {grantRole} and {revokeRole}: this function's purpose is to provide a mechanism for accounts to lose their privileges if they are compromised (such as when a trusted device is misplaced). If the calling account had been revoked `role`, emits a {RoleRevoked} event. Requirements: - the caller must be `callerConfirmation`. May emit a {RoleRevoked} event."}, "reportExecution(uint256,uint256,bool,bytes,uint32,string)": {"details": "Report function execution (gas optimized)"}, "revokeRole(bytes32,address)": {"details": "Revokes `role` from `account`. If `account` had been granted `role`, emits a {RoleRevoked} event. Requirements: - the caller must have ``role``'s admin role. May emit a {RoleRevoked} event."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"remappings": ["@openzeppelin/contracts/=lib/openzeppelin-contracts/contracts/", "erc4626-tests/=lib/openzeppelin-contracts/lib/erc4626-tests/", "forge-std/=lib/forge-std/src/", "halmos-cheatcodes/=lib/openzeppelin-contracts/lib/halmos-cheatcodes/src/", "openzeppelin-contracts/=lib/openzeppelin-contracts/"], "optimizer": {"enabled": true, "runs": 200}, "metadata": {"bytecodeHash": "ipfs"}, "compilationTarget": {"src/OptimizedFunctionRegistry.sol": "OptimizedFunctionRegistry"}, "evmVersion": "shanghai", "libraries": {}}, "sources": {"lib/openzeppelin-contracts/contracts/access/AccessControl.sol": {"keccak256": "0xc1bebdee8943bd5e9ef1e0f2e63296aa1dd4171a66b9e74d0286220e891e1458", "urls": ["bzz-raw://928cf2f0042c606f3dcb21bd8a272573f462a215cd65285d2d6b407f31e9bd67", "dweb:/ipfs/QmWGxjckno6sfjHPX5naPnsfsyisgy4PJDf46eLw9umfpx"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/access/IAccessControl.sol": {"keccak256": "0x4d9a2b261b56a1e4a37bb038151dec98b952fed16de2bdfdda27e38e2b12b530", "urls": ["bzz-raw://f724110f7aeb6151af800ab8c12e6060b29bda9e013f0ccb331eb754d6a7cbf0", "dweb:/ipfs/QmUcjzCZpxtUPdEThtAzE1f9LvuJiUGZxTdH9N6bHrb5Cf"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/ReentrancyGuard.sol": {"keccak256": "0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3", "urls": ["bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a", "dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/ERC165.sol": {"keccak256": "0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa", "urls": ["bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287", "dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p"], "license": "MIT"}, "lib/openzeppelin-contracts/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"], "license": "MIT"}, "src/OptimizedFunctionRegistry.sol": {"keccak256": "0x2a77c792714e62b41f5b80ee7f3a54851e16b5a0305bd78f10fd74365c88e17f", "urls": ["bzz-raw://d63ba57861c5e987655d79a6f77342c2797935a754c628d229670a4faf9e3526", "dweb:/ipfs/QmURcA1fjMPPVVhvo6p3cma84vxSMZPCNbmGXiessY4tBJ"], "license": "MIT"}}, "version": 1}, "id": 24}