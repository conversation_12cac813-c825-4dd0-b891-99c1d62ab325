[profile.default]
src = "src"
out = "out"
libs = ["lib"]
solc_version = "0.8.20"
optimizer = true
optimizer_runs = 200
via_ir = false

# Monad Network Configuration
[rpc_endpoints]
monad_testnet = "https://testnet-rpc.monad.xyz"
monad_mainnet = "https://rpc.monad.xyz"
local = "http://localhost:8545"

# Etherscan configuration for verification
[etherscan]
monad_testnet = { key = "${ETHERSCAN_API_KEY}", url = "https://testnet-explorer.monad.xyz/api" }
monad_mainnet = { key = "${ETHERSCAN_API_KEY}", url = "https://explorer.monad.xyz/api" }

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
