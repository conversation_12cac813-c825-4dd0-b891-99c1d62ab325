const { ethers } = require('ethers');

const CONFIG = {
  rpcUrl: 'http://localhost:8545',
  registryAddress: '******************************************',
  privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
};

const ABI = [
  'function addTrigger(uint256 functionId, uint8 triggerType, bytes calldata triggerData) external returns (uint256 triggerId)',
  'function nextFunctionId() external view returns (uint256)',
  'function nextTriggerId() external view returns (uint256)',
  'function functions(uint256) external view returns (bytes32 wasmHash, address owner, uint96 gasLimit, uint64 createdAt, uint64 executionCount, bool active, string name, string description, string runtime)'
];

async function test() {
  const provider = new ethers.JsonRpcProvider(CONFIG.rpcUrl);
  const wallet = new ethers.Wallet(CONFIG.privateKey, provider);
  const registry = new ethers.Contract(CONFIG.registryAddress, ABI, wallet);

  console.log('Testing optimized contract...');
  
  // Check function count
  const nextFunctionId = await registry.nextFunctionId();
  console.log(`Next Function ID: ${nextFunctionId}`);
  
  // Check if function 1 exists
  const func1 = await registry.functions(1);
  console.log(`Function 1 name: ${func1[6]}`);
  console.log(`Function 1 owner: ${func1[1]}`);
  console.log(`Function 1 active: ${func1[5]}`);
  
  // Try to add a trigger
  try {
    const triggerData = ethers.AbiCoder.defaultAbiCoder().encode(
      ['string', 'uint256', 'string'],
      ['ETH', ethers.parseEther('2000'), 'greater_than']
    );

    console.log('Adding trigger...');
    const tx = await registry.addTrigger(
      1, // functionId
      2, // PRICE_THRESHOLD
      triggerData,
      {
        gasLimit: 200000,
        gasPrice: ethers.parseUnits('25', 'gwei')
      }
    );

    console.log('Transaction sent:', tx.hash);
    const receipt = await tx.wait();
    console.log('Transaction confirmed:', receipt.status);
    
    if (receipt.status === 1) {
      console.log('✅ Trigger added successfully!');
      const nextTriggerId = await registry.nextTriggerId();
      console.log(`Next Trigger ID: ${nextTriggerId}`);
    } else {
      console.log('❌ Transaction failed');
    }

  } catch (error) {
    console.error('❌ Error adding trigger:', error.message);
    if (error.data) {
      console.log('Error data:', error.data);
    }
  }
}

test().catch(console.error);
