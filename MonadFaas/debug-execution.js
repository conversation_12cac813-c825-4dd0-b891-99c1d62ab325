const { ethers } = require('ethers');

// Configuration
const CONFIG = {
  rpcUrl: 'http://localhost:8545',
  registryAddress: '******************************************',
  privateKey: '0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80'
};

// Smart Contract ABI - simplified
const FUNCTION_REGISTRY_ABI = [
  'function reportExecution(uint256 functionId, uint256 triggerId, bool success, bytes calldata returnData, uint256 gasUsed, string calldata errorMessage) external',
  'function functions(uint256) external view returns (bytes32 wasmHash, string name, string description, address owner, uint256 gasLimit, bool active, uint256 createdAt, uint256 executionCount, string runtime)',
  'function triggers(uint256) external view returns (uint256 functionId, uint8 triggerType, bytes triggerData, bool active, uint256 lastTriggered, uint256 triggerCount)',
  'function hasRole(bytes32 role, address account) external view returns (bool)',
  'function ADMIN_ROLE() external view returns (bytes32)',
  'function nextFunctionId() external view returns (uint256)',
  'function nextTriggerId() external view returns (uint256)'
];

async function debugExecution() {
  console.log('🔍 Debugging reportExecution issue...\n');

  // Setup provider and signer
  const provider = new ethers.JsonRpcProvider(CONFIG.rpcUrl);
  const signer = new ethers.Wallet(CONFIG.privateKey, provider);
  const registry = new ethers.Contract(CONFIG.registryAddress, FUNCTION_REGISTRY_ABI, signer);

  console.log('📋 Account Info:');
  console.log(`   Address: ${signer.address}`);
  console.log(`   Balance: ${ethers.formatEther(await provider.getBalance(signer.address))} ETH`);

  // Check admin role
  const adminRole = await registry.ADMIN_ROLE();
  const hasAdminRole = await registry.hasRole(adminRole, signer.address);
  console.log(`   Has Admin Role: ${hasAdminRole}`);
  console.log();

  // Try to call reportExecution with simple parameters
  try {
    console.log('🧪 Testing reportExecution call...');
    
    const functionId = 1; // First function
    const triggerId = 1; // First trigger
    const success = true;
    const returnData = ethers.toUtf8Bytes('Hello World');
    const gasUsed = 50000;
    const errorMessage = '';

    console.log('   Parameters:');
    console.log(`     functionId: ${functionId}`);
    console.log(`     triggerId: ${triggerId}`);
    console.log(`     success: ${success}`);
    console.log(`     returnData: ${ethers.hexlify(returnData)}`);
    console.log(`     gasUsed: ${gasUsed}`);
    console.log(`     errorMessage: "${errorMessage}"`);
    console.log();

    // Check how many functions and triggers exist
    const totalFunctions = await registry.nextFunctionId();
    const totalTriggers = await registry.nextTriggerId();
    console.log(`   Total functions: ${totalFunctions - 1n}`);
    console.log(`   Total triggers: ${totalTriggers - 1n}`);

    // Use the latest function and trigger IDs from the demo
    const latestFunctionId = Number(totalFunctions) - 1;
    const latestTriggerId = Number(totalTriggers) - 1;

    console.log(`   Using functionId: ${latestFunctionId}, triggerId: ${latestTriggerId}`);

    // First, let's check if the function and trigger exist
    try {
      const functionData = await registry.functions(latestFunctionId);
      console.log('✅ Function exists:', functionData[1]); // name is at index 1
    } catch (error) {
      console.log('❌ Function does not exist:', error.message);
      return;
    }

    try {
      const triggerData = await registry.triggers(latestTriggerId);
      console.log('✅ Trigger exists for function:', triggerData[0].toString()); // functionId is at index 0
    } catch (error) {
      console.log('❌ Trigger does not exist:', error.message);
      return;
    }

    // Now try the actual call
    console.log('🚀 Calling reportExecution...');

    console.log(`   Using functionId: ${latestFunctionId}, triggerId: ${latestTriggerId}`);

    const tx = await registry.reportExecution(
      latestFunctionId,
      latestTriggerId,
      success,
      returnData,
      gasUsed,
      errorMessage,
      {
        gasLimit: 300000,
        gasPrice: ethers.parseUnits('25', 'gwei')
      }
    );

    console.log('✅ Transaction sent:', tx.hash);
    const receipt = await tx.wait();
    console.log('✅ Transaction confirmed in block:', receipt.blockNumber);
    console.log('✅ Gas used:', receipt.gasUsed.toString());

  } catch (error) {
    console.log('❌ reportExecution failed:');
    console.log('   Error:', error.message);
    if (error.data) {
      console.log('   Data:', error.data);
    }
    if (error.reason) {
      console.log('   Reason:', error.reason);
    }
  }
}

// Run the debug
debugExecution().catch(console.error);
