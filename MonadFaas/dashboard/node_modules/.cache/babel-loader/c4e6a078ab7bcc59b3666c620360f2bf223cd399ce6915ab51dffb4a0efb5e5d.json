{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Nav.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NavContainer = styled.nav`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem 0;\n  width: 100%;\n  max-width: 1400px;\n  margin: 0 auto;\n`;\n_c = NavContainer;\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n`;\n_c2 = Logo;\nconst LogoImg = styled.div`\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n  \n  svg {\n    width: 100%;\n    height: 100%;\n  }\n`;\n_c3 = LogoImg;\nconst LogoText = styled.span`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n`;\n_c4 = LogoText;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 2rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    display: none;\n  }\n`;\n_c5 = NavLinks;\nconst NavItem = styled.a`\n  color: ${theme.colors.text};\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  padding: 0.5rem;\n  transition: all 0.2s ease;\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  &:hover {\n    color: ${theme.colors.secondary};\n  }\n  \n  svg {\n    width: 16px;\n    height: 16px;\n  }\n`;\n_c6 = NavItem;\nconst DashboardButton = styled.a`\n  background-color: ${theme.colors.secondary};\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 9999px;\n  font-weight: 600;\n  text-decoration: none;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background-color: #7c61ff;\n    transform: translateY(-2px);\n  }\n`;\n_c7 = DashboardButton;\nconst MobileMenuButton = styled.button`\n  display: none;\n  background: none;\n  border: none;\n  color: ${theme.colors.text};\n  font-size: 1.5rem;\n  cursor: pointer;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    display: block;\n  }\n`;\n_c8 = MobileMenuButton;\nconst ChevronIcon = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  viewBox: \"0 0 24 24\",\n  width: \"16\",\n  height: \"16\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  strokeWidth: \"2\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  children: /*#__PURE__*/_jsxDEV(\"polyline\", {\n    points: \"6 9 12 15 18 9\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 99,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 98,\n  columnNumber: 3\n}, this);\n\n// Tenderly logo (simplified version)\n_c9 = ChevronIcon;\nconst TenderlyLogo = () => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"40\",\n  height: \"40\",\n  viewBox: \"0 0 40 40\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 0C8.954 0 0 8.954 0 20C0 31.046 8.954 40 20 40C31.046 40 40 31.046 40 20C40 8.954 31.046 0 20 0ZM27.5 15L20 25L12.5 15H27.5Z\",\n    fill: \"#8A70FF\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 105,\n  columnNumber: 3\n}, this);\n_c0 = TenderlyLogo;\nconst NavBar = () => {\n  _s();\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(NavContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Logo, {\n      children: [/*#__PURE__*/_jsxDEV(LogoImg, {\n        children: /*#__PURE__*/_jsxDEV(TenderlyLogo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoText, {\n        children: \"tenderly\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NavLinks, {\n      children: [/*#__PURE__*/_jsxDEV(NavItem, {\n        href: \"#\",\n        children: [\"Product \", /*#__PURE__*/_jsxDEV(ChevronIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 35\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n        href: \"#\",\n        children: [\"Solutions \", /*#__PURE__*/_jsxDEV(ChevronIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 37\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n        href: \"#\",\n        children: [\"Learn \", /*#__PURE__*/_jsxDEV(ChevronIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavItem, {\n        href: \"#\",\n        children: \"Pricing\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardButton, {\n      href: \"#\",\n      children: \"Go to Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MobileMenuButton, {\n      onClick: () => setMobileMenuOpen(!mobileMenuOpen),\n      children: \"\\u2630\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(NavBar, \"d7gXMF6mPDUhHBNUSEb8mLK4AII=\");\n_c1 = NavBar;\nexport default NavBar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"NavContainer\");\n$RefreshReg$(_c2, \"Logo\");\n$RefreshReg$(_c3, \"LogoImg\");\n$RefreshReg$(_c4, \"LogoText\");\n$RefreshReg$(_c5, \"NavLinks\");\n$RefreshReg$(_c6, \"NavItem\");\n$RefreshReg$(_c7, \"DashboardButton\");\n$RefreshReg$(_c8, \"MobileMenuButton\");\n$RefreshReg$(_c9, \"ChevronIcon\");\n$RefreshReg$(_c0, \"TenderlyLogo\");\n$RefreshReg$(_c1, \"NavBar\");", "map": {"version": 3, "names": ["React", "useState", "styled", "theme", "jsxDEV", "_jsxDEV", "NavContainer", "nav", "_c", "Logo", "div", "_c2", "LogoImg", "_c3", "LogoText", "span", "colors", "text", "_c4", "NavLinks", "breakpoints", "md", "_c5", "NavItem", "a", "secondary", "_c6", "DashboardButton", "_c7", "MobileMenuButton", "button", "_c8", "ChevronIcon", "xmlns", "viewBox", "width", "height", "fill", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "children", "points", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c9", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "d", "_c0", "NavBar", "_s", "mobileMenuOpen", "setMobileMenuOpen", "href", "onClick", "_c1", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Nav.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\n\nconst NavContainer = styled.nav`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1.5rem 0;\n  width: 100%;\n  max-width: 1400px;\n  margin: 0 auto;\n`;\n\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\nconst LogoImg = styled.div`\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n  \n  svg {\n    width: 100%;\n    height: 100%;\n  }\n`;\n\nconst LogoText = styled.span`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n`;\n\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 2rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    display: none;\n  }\n`;\n\nconst NavItem = styled.a`\n  color: ${theme.colors.text};\n  font-size: 1rem;\n  font-weight: 500;\n  text-decoration: none;\n  padding: 0.5rem;\n  transition: all 0.2s ease;\n  position: relative;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  \n  &:hover {\n    color: ${theme.colors.secondary};\n  }\n  \n  svg {\n    width: 16px;\n    height: 16px;\n  }\n`;\n\nconst DashboardButton = styled.a`\n  background-color: ${theme.colors.secondary};\n  color: white;\n  padding: 0.75rem 1.5rem;\n  border-radius: 9999px;\n  font-weight: 600;\n  text-decoration: none;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background-color: #7c61ff;\n    transform: translateY(-2px);\n  }\n`;\n\nconst MobileMenuButton = styled.button`\n  display: none;\n  background: none;\n  border: none;\n  color: ${theme.colors.text};\n  font-size: 1.5rem;\n  cursor: pointer;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    display: block;\n  }\n`;\n\nconst ChevronIcon = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" width=\"16\" height=\"16\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n    <polyline points=\"6 9 12 15 18 9\"></polyline>\n  </svg>\n);\n\n// Tenderly logo (simplified version)\nconst TenderlyLogo = () => (\n  <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M20 0C8.954 0 0 8.954 0 20C0 31.046 8.954 40 20 40C31.046 40 40 31.046 40 20C40 8.954 31.046 0 20 0ZM27.5 15L20 25L12.5 15H27.5Z\" fill=\"#8A70FF\"/>\n  </svg>\n);\n\nconst NavBar: React.FC = () => {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  \n  return (\n    <NavContainer>\n      <Logo>\n        <LogoImg>\n          <TenderlyLogo />\n        </LogoImg>\n        <LogoText>tenderly</LogoText>\n      </Logo>\n      \n      <NavLinks>\n        <NavItem href=\"#\">Product <ChevronIcon /></NavItem>\n        <NavItem href=\"#\">Solutions <ChevronIcon /></NavItem>\n        <NavItem href=\"#\">Learn <ChevronIcon /></NavItem>\n        <NavItem href=\"#\">Pricing</NavItem>\n      </NavLinks>\n      \n      <DashboardButton href=\"#\">Go to Dashboard</DashboardButton>\n      \n      <MobileMenuButton onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>\n        ☰\n      </MobileMenuButton>\n    </NavContainer>\n  );\n};\n\nexport default NavBar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,YAAY,GAAGJ,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,YAAY;AAUlB,MAAMG,IAAI,GAAGP,MAAM,CAACQ,GAAG;AACvB;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,IAAI;AAKV,MAAMG,OAAO,GAAGV,MAAM,CAACQ,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GATID,OAAO;AAWb,MAAME,QAAQ,GAAGZ,MAAM,CAACa,IAAI;AAC5B;AACA;AACA,WAAWZ,KAAK,CAACa,MAAM,CAACC,IAAI;AAC5B,CAAC;AAACC,GAAA,GAJIJ,QAAQ;AAMd,MAAMK,QAAQ,GAAGjB,MAAM,CAACQ,GAAG;AAC3B;AACA;AACA;AACA;AACA,uBAAuBP,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GARIH,QAAQ;AAUd,MAAMI,OAAO,GAAGrB,MAAM,CAACsB,CAAC;AACxB,WAAWrB,KAAK,CAACa,MAAM,CAACC,IAAI;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAad,KAAK,CAACa,MAAM,CAACS,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GApBIH,OAAO;AAsBb,MAAMI,eAAe,GAAGzB,MAAM,CAACsB,CAAC;AAChC,sBAAsBrB,KAAK,CAACa,MAAM,CAACS,SAAS;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAbID,eAAe;AAerB,MAAME,gBAAgB,GAAG3B,MAAM,CAAC4B,MAAM;AACtC;AACA;AACA;AACA,WAAW3B,KAAK,CAACa,MAAM,CAACC,IAAI;AAC5B;AACA;AACA;AACA,uBAAuBd,KAAK,CAACiB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACU,GAAA,GAXIF,gBAAgB;AAatB,MAAMG,WAAW,GAAGA,CAAA,kBAClB3B,OAAA;EAAK4B,KAAK,EAAC,4BAA4B;EAACC,OAAO,EAAC,WAAW;EAACC,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,IAAI,EAAC,MAAM;EAACC,MAAM,EAAC,cAAc;EAACC,WAAW,EAAC,GAAG;EAACC,aAAa,EAAC,OAAO;EAACC,cAAc,EAAC,OAAO;EAAAC,QAAA,eAC/KrC,OAAA;IAAUsC,MAAM,EAAC;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC1C,CACN;;AAED;AAAAC,GAAA,GANMhB,WAAW;AAOjB,MAAMiB,YAAY,GAAGA,CAAA,kBACnB5C,OAAA;EAAK8B,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACF,OAAO,EAAC,WAAW;EAACG,IAAI,EAAC,MAAM;EAACJ,KAAK,EAAC,4BAA4B;EAAAS,QAAA,eAC5FrC,OAAA;IAAM6C,CAAC,EAAC,kIAAkI;IAACb,IAAI,EAAC;EAAS;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxJ,CACN;AAACI,GAAA,GAJIF,YAAY;AAMlB,MAAMG,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAE3D,oBACEI,OAAA,CAACC,YAAY;IAAAoC,QAAA,gBACXrC,OAAA,CAACI,IAAI;MAAAiC,QAAA,gBACHrC,OAAA,CAACO,OAAO;QAAA8B,QAAA,eACNrC,OAAA,CAAC4C,YAAY;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACV1C,OAAA,CAACS,QAAQ;QAAA4B,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAEP1C,OAAA,CAACc,QAAQ;MAAAuB,QAAA,gBACPrC,OAAA,CAACkB,OAAO;QAACiC,IAAI,EAAC,GAAG;QAAAd,QAAA,GAAC,UAAQ,eAAArC,OAAA,CAAC2B,WAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACnD1C,OAAA,CAACkB,OAAO;QAACiC,IAAI,EAAC,GAAG;QAAAd,QAAA,GAAC,YAAU,eAAArC,OAAA,CAAC2B,WAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACrD1C,OAAA,CAACkB,OAAO;QAACiC,IAAI,EAAC,GAAG;QAAAd,QAAA,GAAC,QAAM,eAAArC,OAAA,CAAC2B,WAAW;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eACjD1C,OAAA,CAACkB,OAAO;QAACiC,IAAI,EAAC,GAAG;QAAAd,QAAA,EAAC;MAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAEX1C,OAAA,CAACsB,eAAe;MAAC6B,IAAI,EAAC,GAAG;MAAAd,QAAA,EAAC;IAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAiB,CAAC,eAE3D1C,OAAA,CAACwB,gBAAgB;MAAC4B,OAAO,EAAEA,CAAA,KAAMF,iBAAiB,CAAC,CAACD,cAAc,CAAE;MAAAZ,QAAA,EAAC;IAErE;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAkB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEnB,CAAC;AAACM,EAAA,CA1BID,MAAgB;AAAAM,GAAA,GAAhBN,MAAgB;AA4BtB,eAAeA,MAAM;AAAC,IAAA5C,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAiB,GAAA,EAAAG,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAnD,EAAA;AAAAmD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}