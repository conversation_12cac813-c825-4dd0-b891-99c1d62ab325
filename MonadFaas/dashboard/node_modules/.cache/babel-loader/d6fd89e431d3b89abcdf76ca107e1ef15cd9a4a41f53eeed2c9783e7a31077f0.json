{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BackgroundEffect.tsx\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst pulseGlow = keyframes`\n  0%, 100% {\n    opacity: 0.2;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.4;\n    transform: scale(1.1);\n  }\n`;\nconst rotate = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\nconst Container = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  z-index: 0;\n  pointer-events: none;\n`;\n_c = Container;\nconst OrbitRing = styled.div`\n  position: absolute;\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  border: 1px solid rgba(138, 112, 255, 0.1);\n  animation: ${rotate} ${props => props.duration}s linear infinite;\n  animation-delay: ${props => props.delay}s;\n  transform-origin: center;\n`;\n_c2 = OrbitRing;\nconst GlowingOrb = styled.div`\n  position: absolute;\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: radial-gradient(circle at center, ${props => props.color} 0%, transparent 70%);\n  filter: blur(20px);\n  opacity: 0.3;\n  animation: ${pulseGlow} ${props => props.duration}s ease-in-out infinite;\n`;\n_c3 = GlowingOrb;\nconst GridLine = styled.div`\n  position: absolute;\n  ${props => props.direction === 'horizontal' ? `\n    top: ${props.position}%;\n    left: 0;\n    width: 100%;\n    height: 1px;\n  ` : `\n    top: 0;\n    left: ${props.position}%;\n    width: 1px;\n    height: 100%;\n  `}\n  background-color: rgba(138, 112, 255, ${props => props.opacity});\n  opacity: 0.2;\n`;\n_c4 = GridLine;\nconst BackgroundEffect = () => {\n  // Generate random orbit rings\n  const renderOrbitRings = () => {\n    const rings = [];\n    for (let i = 0; i < 3; i++) {\n      rings.push(/*#__PURE__*/_jsxDEV(OrbitRing, {\n        size: 300 + i * 150,\n        top: Math.random() * 80,\n        left: Math.random() * 80,\n        duration: 60 + i * 20,\n        delay: i * 5\n      }, `ring-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this));\n    }\n    return rings;\n  };\n\n  // Generate glowing orbs\n  const renderGlowingOrbs = () => {\n    const orbs = [];\n    const colors = [theme.colors.secondary, theme.colors.primary, `${theme.colors.secondary}`, `${theme.colors.primary}`];\n    for (let i = 0; i < 6; i++) {\n      orbs.push(/*#__PURE__*/_jsxDEV(GlowingOrb, {\n        size: 100 + i * 40,\n        top: Math.random() * 80,\n        left: Math.random() * 80,\n        color: colors[i % colors.length],\n        duration: 4 + i * 2\n      }, `orb-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this));\n    }\n    return orbs;\n  };\n\n  // Generate grid lines\n  const renderGridLines = () => {\n    const lines = [];\n\n    // Horizontal lines\n    for (let i = 0; i < 5; i++) {\n      lines.push(/*#__PURE__*/_jsxDEV(GridLine, {\n        direction: \"horizontal\",\n        position: 10 + i * 20,\n        width: 100,\n        opacity: 0.05 + Math.random() * 0.1\n      }, `h-line-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this));\n    }\n\n    // Vertical lines\n    for (let i = 0; i < 5; i++) {\n      lines.push(/*#__PURE__*/_jsxDEV(GridLine, {\n        direction: \"vertical\",\n        position: 10 + i * 20,\n        width: 100,\n        opacity: 0.05 + Math.random() * 0.1\n      }, `v-line-${i}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this));\n    }\n    return lines;\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [renderGlowingOrbs(), renderOrbitRings(), renderGridLines()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 157,\n    columnNumber: 5\n  }, this);\n};\n_c5 = BackgroundEffect;\nexport default BackgroundEffect;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"OrbitRing\");\n$RefreshReg$(_c3, \"GlowingOrb\");\n$RefreshReg$(_c4, \"GridLine\");\n$RefreshReg$(_c5, \"BackgroundEffect\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "theme", "jsxDEV", "_jsxDEV", "pulseGlow", "rotate", "Container", "div", "_c", "OrbitRing", "props", "top", "left", "size", "duration", "delay", "_c2", "GlowingOrb", "color", "_c3", "GridLine", "direction", "position", "opacity", "_c4", "BackgroundEffect", "renderOrbitRings", "rings", "i", "push", "Math", "random", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderGlowingOrbs", "orbs", "colors", "secondary", "primary", "length", "renderGridLines", "lines", "width", "children", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BackgroundEffect.tsx"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\nconst pulseGlow = keyframes`\n  0%, 100% {\n    opacity: 0.2;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.4;\n    transform: scale(1.1);\n  }\n`;\n\nconst rotate = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\n\nconst Container = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  z-index: 0;\n  pointer-events: none;\n`;\n\nconst OrbitRing = styled.div<{ size: number; top: number; left: number; duration: number; delay: number }>`\n  position: absolute;\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  border: 1px solid rgba(138, 112, 255, 0.1);\n  animation: ${rotate} ${props => props.duration}s linear infinite;\n  animation-delay: ${props => props.delay}s;\n  transform-origin: center;\n`;\n\nconst GlowingOrb = styled.div<{ size: number; top: number; left: number; color: string; duration: number }>`\n  position: absolute;\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: radial-gradient(circle at center, ${props => props.color} 0%, transparent 70%);\n  filter: blur(20px);\n  opacity: 0.3;\n  animation: ${pulseGlow} ${props => props.duration}s ease-in-out infinite;\n`;\n\nconst GridLine = styled.div<{ direction: 'horizontal' | 'vertical'; position: number; width: number; opacity: number }>`\n  position: absolute;\n  ${props => props.direction === 'horizontal' ? `\n    top: ${props.position}%;\n    left: 0;\n    width: 100%;\n    height: 1px;\n  ` : `\n    top: 0;\n    left: ${props.position}%;\n    width: 1px;\n    height: 100%;\n  `}\n  background-color: rgba(138, 112, 255, ${props => props.opacity});\n  opacity: 0.2;\n`;\n\nconst BackgroundEffect: React.FC = () => {\n  // Generate random orbit rings\n  const renderOrbitRings = () => {\n    const rings = [];\n    for (let i = 0; i < 3; i++) {\n      rings.push(\n        <OrbitRing\n          key={`ring-${i}`}\n          size={300 + (i * 150)}\n          top={Math.random() * 80}\n          left={Math.random() * 80}\n          duration={60 + (i * 20)}\n          delay={i * 5}\n        />\n      );\n    }\n    return rings;\n  };\n\n  // Generate glowing orbs\n  const renderGlowingOrbs = () => {\n    const orbs = [];\n    const colors = [\n      theme.colors.secondary,\n      theme.colors.primary,\n      `${theme.colors.secondary}`,\n      `${theme.colors.primary}`\n    ];\n    \n    for (let i = 0; i < 6; i++) {\n      orbs.push(\n        <GlowingOrb\n          key={`orb-${i}`}\n          size={100 + (i * 40)}\n          top={Math.random() * 80}\n          left={Math.random() * 80}\n          color={colors[i % colors.length]}\n          duration={4 + (i * 2)}\n        />\n      );\n    }\n    return orbs;\n  };\n\n  // Generate grid lines\n  const renderGridLines = () => {\n    const lines = [];\n    \n    // Horizontal lines\n    for (let i = 0; i < 5; i++) {\n      lines.push(\n        <GridLine\n          key={`h-line-${i}`}\n          direction=\"horizontal\"\n          position={10 + (i * 20)}\n          width={100}\n          opacity={0.05 + (Math.random() * 0.1)}\n        />\n      );\n    }\n    \n    // Vertical lines\n    for (let i = 0; i < 5; i++) {\n      lines.push(\n        <GridLine\n          key={`v-line-${i}`}\n          direction=\"vertical\"\n          position={10 + (i * 20)}\n          width={100}\n          opacity={0.05 + (Math.random() * 0.1)}\n        />\n      );\n    }\n    \n    return lines;\n  };\n\n  return (\n    <Container>\n      {renderGlowingOrbs()}\n      {renderOrbitRings()}\n      {renderGridLines()}\n    </Container>\n  );\n};\n\nexport default BackgroundEffect; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,SAAS,GAAGJ,SAAS;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,MAAM,GAAGL,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,SAAS,GAAGP,MAAM,CAACQ,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GATIF,SAAS;AAWf,MAAMG,SAAS,GAAGV,MAAM,CAACQ,GAAiF;AAC1G;AACA,SAASG,KAAK,IAAIA,KAAK,CAACC,GAAG;AAC3B,UAAUD,KAAK,IAAIA,KAAK,CAACE,IAAI;AAC7B,WAAWF,KAAK,IAAIA,KAAK,CAACG,IAAI;AAC9B,YAAYH,KAAK,IAAIA,KAAK,CAACG,IAAI;AAC/B;AACA;AACA,eAAeR,MAAM,IAAIK,KAAK,IAAIA,KAAK,CAACI,QAAQ;AAChD,qBAAqBJ,KAAK,IAAIA,KAAK,CAACK,KAAK;AACzC;AACA,CAAC;AAACC,GAAA,GAXIP,SAAS;AAaf,MAAMQ,UAAU,GAAGlB,MAAM,CAACQ,GAAiF;AAC3G;AACA,SAASG,KAAK,IAAIA,KAAK,CAACC,GAAG;AAC3B,UAAUD,KAAK,IAAIA,KAAK,CAACE,IAAI;AAC7B,WAAWF,KAAK,IAAIA,KAAK,CAACG,IAAI;AAC9B,YAAYH,KAAK,IAAIA,KAAK,CAACG,IAAI;AAC/B;AACA,kDAAkDH,KAAK,IAAIA,KAAK,CAACQ,KAAK;AACtE;AACA;AACA,eAAed,SAAS,IAAIM,KAAK,IAAIA,KAAK,CAACI,QAAQ;AACnD,CAAC;AAACK,GAAA,GAXIF,UAAU;AAahB,MAAMG,QAAQ,GAAGrB,MAAM,CAACQ,GAA+F;AACvH;AACA,IAAIG,KAAK,IAAIA,KAAK,CAACW,SAAS,KAAK,YAAY,GAAG;AAChD,WAAWX,KAAK,CAACY,QAAQ;AACzB;AACA;AACA;AACA,GAAG,GAAG;AACN;AACA,YAAYZ,KAAK,CAACY,QAAQ;AAC1B;AACA;AACA,GAAG;AACH,0CAA0CZ,KAAK,IAAIA,KAAK,CAACa,OAAO;AAChE;AACA,CAAC;AAACC,GAAA,GAfIJ,QAAQ;AAiBd,MAAMK,gBAA0B,GAAGA,CAAA,KAAM;EACvC;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,KAAK,GAAG,EAAE;IAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BD,KAAK,CAACE,IAAI,cACR1B,OAAA,CAACM,SAAS;QAERI,IAAI,EAAE,GAAG,GAAIe,CAAC,GAAG,GAAK;QACtBjB,GAAG,EAAEmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG;QACxBnB,IAAI,EAAEkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG;QACzBjB,QAAQ,EAAE,EAAE,GAAIc,CAAC,GAAG,EAAI;QACxBb,KAAK,EAAEa,CAAC,GAAG;MAAE,GALR,QAAQA,CAAC,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMjB,CACH,CAAC;IACH;IACA,OAAOR,KAAK;EACd,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,MAAM,GAAG,CACbrC,KAAK,CAACqC,MAAM,CAACC,SAAS,EACtBtC,KAAK,CAACqC,MAAM,CAACE,OAAO,EACpB,GAAGvC,KAAK,CAACqC,MAAM,CAACC,SAAS,EAAE,EAC3B,GAAGtC,KAAK,CAACqC,MAAM,CAACE,OAAO,EAAE,CAC1B;IAED,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BS,IAAI,CAACR,IAAI,cACP1B,OAAA,CAACc,UAAU;QAETJ,IAAI,EAAE,GAAG,GAAIe,CAAC,GAAG,EAAI;QACrBjB,GAAG,EAAEmB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG;QACxBnB,IAAI,EAAEkB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAG;QACzBb,KAAK,EAAEoB,MAAM,CAACV,CAAC,GAAGU,MAAM,CAACG,MAAM,CAAE;QACjC3B,QAAQ,EAAE,CAAC,GAAIc,CAAC,GAAG;MAAG,GALjB,OAAOA,CAAC,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMhB,CACH,CAAC;IACH;IACA,OAAOE,IAAI;EACb,CAAC;;EAED;EACA,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5B,MAAMC,KAAK,GAAG,EAAE;;IAEhB;IACA,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1Be,KAAK,CAACd,IAAI,cACR1B,OAAA,CAACiB,QAAQ;QAEPC,SAAS,EAAC,YAAY;QACtBC,QAAQ,EAAE,EAAE,GAAIM,CAAC,GAAG,EAAI;QACxBgB,KAAK,EAAE,GAAI;QACXrB,OAAO,EAAE,IAAI,GAAIO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MAAK,GAJjC,UAAUH,CAAC,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKnB,CACH,CAAC;IACH;;IAEA;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1Be,KAAK,CAACd,IAAI,cACR1B,OAAA,CAACiB,QAAQ;QAEPC,SAAS,EAAC,UAAU;QACpBC,QAAQ,EAAE,EAAE,GAAIM,CAAC,GAAG,EAAI;QACxBgB,KAAK,EAAE,GAAI;QACXrB,OAAO,EAAE,IAAI,GAAIO,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MAAK,GAJjC,UAAUH,CAAC,EAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKnB,CACH,CAAC;IACH;IAEA,OAAOQ,KAAK;EACd,CAAC;EAED,oBACExC,OAAA,CAACG,SAAS;IAAAuC,QAAA,GACPT,iBAAiB,CAAC,CAAC,EACnBV,gBAAgB,CAAC,CAAC,EAClBgB,eAAe,CAAC,CAAC;EAAA;IAAAV,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEhB,CAAC;AAACW,GAAA,GApFIrB,gBAA0B;AAsFhC,eAAeA,gBAAgB;AAAC,IAAAjB,EAAA,EAAAQ,GAAA,EAAAG,GAAA,EAAAK,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}