{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BenefitItem.tsx\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport EnhancedImage from './EnhancedImage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst float = keyframes`\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\nconst pulse = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 0 0 rgba(138, 112, 255, 0);\n  }\n  50% {\n    box-shadow: 0 0 20px 5px rgba(138, 112, 255, 0.3);\n  }\n`;\nconst ItemContainer = styled.div`\n  background-color: rgba(18, 18, 30, 0.6);\n  border-radius: 12px;\n  overflow: hidden;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  border: 1px solid rgba(138, 112, 255, 0.1);\n  z-index: 1;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n    \n    .image-container {\n      transform: scale(1.05);\n    }\n    \n    .title {\n      color: ${theme.colors.secondary};\n    }\n  }\n`;\n_c = ItemContainer;\nconst ImageContainer = styled.div`\n  width: 100%;\n  height: 240px;\n  overflow: hidden;\n  background-color: #0c0c14;\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 2rem;\n  transition: transform 0.5s ease;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: radial-gradient(circle at center, rgba(0, 210, 194, 0.1) 0%, transparent 70%);\n    z-index: 0;\n  }\n`;\n_c2 = ImageContainer;\nconst GlowingBorder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(to right, transparent, ${theme.colors.secondary}, transparent);\n  opacity: 0.7;\n  animation: ${pulse} 3s ease-in-out infinite;\n`;\n_c3 = GlowingBorder;\nconst ContentContainer = styled.div`\n  padding: 2rem;\n`;\n_c4 = ContentContainer;\nconst Title = styled.h3`\n  font-size: 1.75rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: ${theme.colors.text};\n  transition: color 0.3s ease;\n`;\n_c5 = Title;\nconst Description = styled.p`\n  font-size: 1rem;\n  line-height: 1.6;\n  color: ${theme.colors.textSecondary};\n`;\n_c6 = Description;\nconst BenefitItem = ({\n  title,\n  description,\n  image\n}) => {\n  return /*#__PURE__*/_jsxDEV(ItemContainer, {\n    children: [/*#__PURE__*/_jsxDEV(GlowingBorder, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ImageContainer, {\n      className: \"image-container\",\n      children: /*#__PURE__*/_jsxDEV(EnhancedImage, {\n        src: image,\n        alt: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentContainer, {\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        className: \"title\",\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Description, {\n        children: description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_c7 = BenefitItem;\nexport default BenefitItem;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ItemContainer\");\n$RefreshReg$(_c2, \"ImageContainer\");\n$RefreshReg$(_c3, \"GlowingBorder\");\n$RefreshReg$(_c4, \"ContentContainer\");\n$RefreshReg$(_c5, \"Title\");\n$RefreshReg$(_c6, \"Description\");\n$RefreshReg$(_c7, \"BenefitItem\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "theme", "EnhancedImage", "jsxDEV", "_jsxDEV", "float", "pulse", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "colors", "secondary", "_c", "ImageContainer", "_c2", "GlowingBorder", "_c3", "ContentContainer", "_c4", "Title", "h3", "text", "_c5", "Description", "p", "textSecondary", "_c6", "BenefitItem", "title", "description", "image", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "src", "alt", "_c7", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BenefitItem.tsx"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport EnhancedImage from './EnhancedImage';\n\ninterface BenefitItemProps {\n  title: string;\n  description: string;\n  image: string;\n}\n\nconst float = keyframes`\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 0 0 rgba(138, 112, 255, 0);\n  }\n  50% {\n    box-shadow: 0 0 20px 5px rgba(138, 112, 255, 0.3);\n  }\n`;\n\nconst ItemContainer = styled.div`\n  background-color: rgba(18, 18, 30, 0.6);\n  border-radius: 12px;\n  overflow: hidden;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  border: 1px solid rgba(138, 112, 255, 0.1);\n  z-index: 1;\n  \n  &:hover {\n    transform: translateY(-5px);\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n    \n    .image-container {\n      transform: scale(1.05);\n    }\n    \n    .title {\n      color: ${theme.colors.secondary};\n    }\n  }\n`;\n\nconst ImageContainer = styled.div`\n  width: 100%;\n  height: 240px;\n  overflow: hidden;\n  background-color: #0c0c14;\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 2rem;\n  transition: transform 0.5s ease;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: radial-gradient(circle at center, rgba(0, 210, 194, 0.1) 0%, transparent 70%);\n    z-index: 0;\n  }\n`;\n\nconst GlowingBorder = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 4px;\n  background: linear-gradient(to right, transparent, ${theme.colors.secondary}, transparent);\n  opacity: 0.7;\n  animation: ${pulse} 3s ease-in-out infinite;\n`;\n\nconst ContentContainer = styled.div`\n  padding: 2rem;\n`;\n\nconst Title = styled.h3`\n  font-size: 1.75rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n  color: ${theme.colors.text};\n  transition: color 0.3s ease;\n`;\n\nconst Description = styled.p`\n  font-size: 1rem;\n  line-height: 1.6;\n  color: ${theme.colors.textSecondary};\n`;\n\nconst BenefitItem: React.FC<BenefitItemProps> = ({ title, description, image }) => {\n  return (\n    <ItemContainer>\n      <GlowingBorder />\n      <ImageContainer className=\"image-container\">\n        <EnhancedImage src={image} alt={title} />\n      </ImageContainer>\n      <ContentContainer>\n        <Title className=\"title\">{title}</Title>\n        <Description>{description}</Description>\n      </ContentContainer>\n    </ItemContainer>\n  );\n};\n\nexport default BenefitItem; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAOC,aAAa,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ5C,MAAMC,KAAK,GAAGL,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,KAAK,GAAGN,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,aAAa,GAAGR,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeP,KAAK,CAACQ,MAAM,CAACC,SAAS;AACrC;AACA;AACA,CAAC;AAACC,EAAA,GAvBIJ,aAAa;AAyBnB,MAAMK,cAAc,GAAGb,MAAM,CAACS,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAtBID,cAAc;AAwBpB,MAAME,aAAa,GAAGf,MAAM,CAACS,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,uDAAuDP,KAAK,CAACQ,MAAM,CAACC,SAAS;AAC7E;AACA,eAAeJ,KAAK;AACpB,CAAC;AAACS,GAAA,GATID,aAAa;AAWnB,MAAME,gBAAgB,GAAGjB,MAAM,CAACS,GAAG;AACnC;AACA,CAAC;AAACS,GAAA,GAFID,gBAAgB;AAItB,MAAME,KAAK,GAAGnB,MAAM,CAACoB,EAAE;AACvB;AACA;AACA;AACA,WAAWlB,KAAK,CAACQ,MAAM,CAACW,IAAI;AAC5B;AACA,CAAC;AAACC,GAAA,GANIH,KAAK;AAQX,MAAMI,WAAW,GAAGvB,MAAM,CAACwB,CAAC;AAC5B;AACA;AACA,WAAWtB,KAAK,CAACQ,MAAM,CAACe,aAAa;AACrC,CAAC;AAACC,GAAA,GAJIH,WAAW;AAMjB,MAAMI,WAAuC,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAM,CAAC,KAAK;EACjF,oBACEzB,OAAA,CAACG,aAAa;IAAAuB,QAAA,gBACZ1B,OAAA,CAACU,aAAa;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjB9B,OAAA,CAACQ,cAAc;MAACuB,SAAS,EAAC,iBAAiB;MAAAL,QAAA,eACzC1B,OAAA,CAACF,aAAa;QAACkC,GAAG,EAAEP,KAAM;QAACQ,GAAG,EAAEV;MAAM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eACjB9B,OAAA,CAACY,gBAAgB;MAAAc,QAAA,gBACf1B,OAAA,CAACc,KAAK;QAACiB,SAAS,EAAC,OAAO;QAAAL,QAAA,EAAEH;MAAK;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxC9B,OAAA,CAACkB,WAAW;QAAAQ,QAAA,EAAEF;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEpB,CAAC;AAACI,GAAA,GAbIZ,WAAuC;AAe7C,eAAeA,WAAW;AAAC,IAAAf,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAA1B,GAAA;AAAA0B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}