{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/ClientLogos.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LogosContainer = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n  overflow: hidden;\n`;\n_c = LogosContainer;\nconst LogosGrid = styled.div`\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  gap: 3rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    gap: 2rem;\n  }\n`;\n_c2 = LogosGrid;\nconst LogoItem = styled.div`\n  opacity: 0.7;\n  transition: opacity 0.2s ease;\n  filter: grayscale(100%);\n  \n  &:hover {\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n`;\n\n// Company logo components\n_c3 = LogoItem;\nconst CompanyLogo = ({\n  name\n}) => {\n  const logoStyle = {\n    color: 'white',\n    fontWeight: 'bold',\n    fontSize: '1.2rem'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: logoStyle,\n    children: name\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 10\n  }, this);\n};\n_c4 = CompanyLogo;\nconst ClientLogos = () => {\n  return /*#__PURE__*/_jsxDEV(LogosContainer, {\n    children: /*#__PURE__*/_jsxDEV(LogosGrid, {\n      children: [/*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"induit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"Chainlink\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"Uniswap\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"aave\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"MAKER\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"Safe\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LogoItem, {\n        children: /*#__PURE__*/_jsxDEV(CompanyLogo, {\n          name: \"world\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c5 = ClientLogos;\nexport default ClientLogos;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"LogosContainer\");\n$RefreshReg$(_c2, \"LogosGrid\");\n$RefreshReg$(_c3, \"LogoItem\");\n$RefreshReg$(_c4, \"CompanyLogo\");\n$RefreshReg$(_c5, \"ClientLogos\");", "map": {"version": 3, "names": ["React", "styled", "theme", "jsxDEV", "_jsxDEV", "LogosContainer", "div", "_c", "LogosGrid", "breakpoints", "md", "_c2", "LogoItem", "_c3", "CompanyLogo", "name", "logoStyle", "color", "fontWeight", "fontSize", "style", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c4", "Client<PERSON><PERSON><PERSON>", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/ClientLogos.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\n\nconst LogosContainer = styled.div`\n  display: flex;\n  align-items: center;\n  width: 100%;\n  overflow: hidden;\n`;\n\nconst LogosGrid = styled.div`\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  gap: 3rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    gap: 2rem;\n  }\n`;\n\nconst LogoItem = styled.div`\n  opacity: 0.7;\n  transition: opacity 0.2s ease;\n  filter: grayscale(100%);\n  \n  &:hover {\n    opacity: 1;\n    filter: grayscale(0%);\n  }\n`;\n\n// Company logo components\nconst CompanyLogo = ({ name }: { name: string }) => {\n  const logoStyle = {\n    color: 'white',\n    fontWeight: 'bold',\n    fontSize: '1.2rem'\n  };\n  \n  return <div style={logoStyle}>{name}</div>;\n};\n\nconst ClientLogos: React.FC = () => {\n  return (\n    <LogosContainer>\n      <LogosGrid>\n        <LogoItem><CompanyLogo name=\"induit\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"Chainlink\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"Uniswap\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"aave\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"MAKER\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"Safe\" /></LogoItem>\n        <LogoItem><CompanyLogo name=\"world\" /></LogoItem>\n      </LogosGrid>\n    </LogosContainer>\n  );\n};\n\nexport default ClientLogos; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGJ,MAAM,CAACK,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,cAAc;AAOpB,MAAMG,SAAS,GAAGP,MAAM,CAACK,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,uBAAuBJ,KAAK,CAACO,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GATIH,SAAS;AAWf,MAAMI,QAAQ,GAAGX,MAAM,CAACK,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAXMD,QAAQ;AAYd,MAAME,WAAW,GAAGA,CAAC;EAAEC;AAAuB,CAAC,KAAK;EAClD,MAAMC,SAAS,GAAG;IAChBC,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE;EACZ,CAAC;EAED,oBAAOf,OAAA;IAAKgB,KAAK,EAAEJ,SAAU;IAAAK,QAAA,EAAEN;EAAI;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;AAC5C,CAAC;AAACC,GAAA,GARIZ,WAAW;AAUjB,MAAMa,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACEvB,OAAA,CAACC,cAAc;IAAAgB,QAAA,eACbjB,OAAA,CAACI,SAAS;MAAAa,QAAA,gBACRjB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAQ;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAClDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAW;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACrDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAS;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACnDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAChDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACjDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAM;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eAChDrB,OAAA,CAACQ,QAAQ;QAAAS,QAAA,eAACjB,OAAA,CAACU,WAAW;UAACC,IAAI,EAAC;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAACG,GAAA,GAdID,WAAqB;AAgB3B,eAAeA,WAAW;AAAC,IAAApB,EAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAa,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}