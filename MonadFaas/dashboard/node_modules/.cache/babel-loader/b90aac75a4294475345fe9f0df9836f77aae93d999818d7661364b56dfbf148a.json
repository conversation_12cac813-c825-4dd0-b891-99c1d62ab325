{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/SectionHeader.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeaderContainer = styled.div`\n  text-align: center;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n`;\n_c = HeaderContainer;\nconst Title = styled.h2`\n  font-size: 3.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: ${theme.colors.text};\n  line-height: 1.2;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 2.5rem;\n  }\n`;\n_c2 = Title;\nconst Subtitle = styled.p`\n  font-size: 1.25rem;\n  color: ${theme.colors.textSecondary};\n  line-height: 1.6;\n  margin-bottom: 1rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 1.1rem;\n  }\n`;\n_c3 = Subtitle;\nconst Accent = styled.div`\n  width: 60px;\n  height: 4px;\n  background: linear-gradient(to right, ${theme.colors.primary}, ${theme.colors.secondary});\n  margin: 0 auto;\n  border-radius: 4px;\n`;\n_c4 = Accent;\nconst SectionHeader = ({\n  title,\n  subtitle\n}) => {\n  return /*#__PURE__*/_jsxDEV(HeaderContainer, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), subtitle && /*#__PURE__*/_jsxDEV(Subtitle, {\n      children: subtitle\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 20\n    }, this), /*#__PURE__*/_jsxDEV(Accent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_c5 = SectionHeader;\nexport default SectionHeader;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"HeaderContainer\");\n$RefreshReg$(_c2, \"Title\");\n$RefreshReg$(_c3, \"Subtitle\");\n$RefreshReg$(_c4, \"Accent\");\n$RefreshReg$(_c5, \"SectionHeader\");", "map": {"version": 3, "names": ["React", "styled", "theme", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "Title", "h2", "colors", "text", "breakpoints", "md", "_c2", "Subtitle", "p", "textSecondary", "_c3", "Accent", "primary", "secondary", "_c4", "SectionHeader", "title", "subtitle", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/SectionHeader.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\n\ninterface SectionHeaderProps {\n  title: string;\n  subtitle?: string;\n}\n\nconst HeaderContainer = styled.div`\n  text-align: center;\n  max-width: 800px;\n  margin: 0 auto;\n  position: relative;\n  z-index: 1;\n`;\n\nconst Title = styled.h2`\n  font-size: 3.5rem;\n  font-weight: 700;\n  margin-bottom: 1.5rem;\n  color: ${theme.colors.text};\n  line-height: 1.2;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 2.5rem;\n  }\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.25rem;\n  color: ${theme.colors.textSecondary};\n  line-height: 1.6;\n  margin-bottom: 1rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 1.1rem;\n  }\n`;\n\nconst Accent = styled.div`\n  width: 60px;\n  height: 4px;\n  background: linear-gradient(to right, ${theme.colors.primary}, ${theme.colors.secondary});\n  margin: 0 auto;\n  border-radius: 4px;\n`;\n\nconst SectionHeader: React.FC<SectionHeaderProps> = ({ title, subtitle }) => {\n  return (\n    <HeaderContainer>\n      <Title>{title}</Title>\n      {subtitle && <Subtitle>{subtitle}</Subtitle>}\n      <Accent />\n    </HeaderContainer>\n  );\n};\n\nexport default SectionHeader; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO3C,MAAMC,eAAe,GAAGJ,MAAM,CAACK,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,eAAe;AAQrB,MAAMG,KAAK,GAAGP,MAAM,CAACQ,EAAE;AACvB;AACA;AACA;AACA,WAAWP,KAAK,CAACQ,MAAM,CAACC,IAAI;AAC5B;AACA;AACA,uBAAuBT,KAAK,CAACU,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GAVIN,KAAK;AAYX,MAAMO,QAAQ,GAAGd,MAAM,CAACe,CAAC;AACzB;AACA,WAAWd,KAAK,CAACQ,MAAM,CAACO,aAAa;AACrC;AACA;AACA;AACA,uBAAuBf,KAAK,CAACU,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACK,GAAA,GATIH,QAAQ;AAWd,MAAMI,MAAM,GAAGlB,MAAM,CAACK,GAAG;AACzB;AACA;AACA,0CAA0CJ,KAAK,CAACQ,MAAM,CAACU,OAAO,KAAKlB,KAAK,CAACQ,MAAM,CAACW,SAAS;AACzF;AACA;AACA,CAAC;AAACC,GAAA,GANIH,MAAM;AAQZ,MAAMI,aAA2C,GAAGA,CAAC;EAAEC,KAAK;EAAEC;AAAS,CAAC,KAAK;EAC3E,oBACErB,OAAA,CAACC,eAAe;IAAAqB,QAAA,gBACdtB,OAAA,CAACI,KAAK;MAAAkB,QAAA,EAAEF;IAAK;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EACrBL,QAAQ,iBAAIrB,OAAA,CAACW,QAAQ;MAAAW,QAAA,EAAED;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAW,CAAC,eAC5C1B,OAAA,CAACe,MAAM;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEtB,CAAC;AAACC,GAAA,GARIR,aAA2C;AAUjD,eAAeA,aAAa;AAAC,IAAAhB,EAAA,EAAAO,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}