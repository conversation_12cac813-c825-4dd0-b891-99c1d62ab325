{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Explorer/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef, useEffect, useState } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\n// Import images from assets\nimport explorerImage1 from '../../assets/home-explorer-1-mob.webp';\nimport explorerImage2 from '../../assets/home-explorer-2-mob.webp';\nimport explorerImage3 from '../../assets/home-explorer-3.webp';\nimport explorerImage4 from '../../assets/home-explorer-4.webp';\nimport explorerImage5 from '../../assets/home-explorer-5-mob.webp';\n\n// Animation keyframes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\nconst fadeInLeft = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`;\nconst fadeInRight = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`;\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;\nconst bounce = keyframes`\n  from {\n    transform: translateY(0);\n  }\n  to {\n    transform: translateY(5px);\n  }\n`;\n\n// Styled components\nconst ExplorerSectionContainer = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  padding: 4rem 0;\n  position: relative;\n  background-color: ${theme.colors.background};\n`;\n_c = ExplorerSectionContainer;\nconst ContentContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  position: relative;\n  z-index: 2;\n`;\n_c2 = ContentContainer;\nconst SectionHeader = styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n  animation: ${fadeIn} 0.8s forwards ease-out;\n`;\n_c3 = SectionHeader;\nconst Title = styled.h2`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1rem;\n  line-height: 1.2;\n  \n  span {\n    color: ${theme.colors.secondary};\n    display: inline-block;\n  }\n`;\n_c4 = Title;\nconst Subtitle = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n`;\n_c5 = Subtitle;\nconst ExplorerContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1.5fr;\n  gap: 3rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n`;\n_c6 = ExplorerContent;\nconst TimelineContainer = styled.div`\n  position: sticky;\n  top: 120px;\n  height: calc(100vh - 300px);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  animation: ${fadeInLeft} 1s forwards;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    position: relative;\n    top: 0;\n    height: auto;\n    margin-bottom: 2rem;\n  }\n`;\n_c7 = TimelineContainer;\nconst ImagesContainer = styled.div`\n  position: relative;\n  min-height: 400px;\n  animation: ${fadeInRight} 1s forwards;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    min-height: 300px;\n  }\n`;\n_c8 = ImagesContainer;\nconst ProgressDots = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 0.5rem;\n  margin-top: 1.5rem;\n`;\n_c9 = ProgressDots;\nconst Dot = styled.button`\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background-color: ${props => props.isActive ? theme.colors.secondary : theme.colors.backgroundMedium};\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  transition: transform 0.2s, background-color 0.2s;\n\n  &:hover {\n    transform: scale(1.2);\n  }\n`;\n\n// Timeline Item components\n_c0 = Dot;\nconst TimelineItem = styled.div`\n  padding: 1.5rem;\n  position: relative;\n  padding-left: 3rem;\n  margin-bottom: 0.5rem;\n  background-color: ${props => props.isActive ? 'rgba(138, 112, 255, 0.1)' : 'transparent'};\n  border-radius: ${theme.borderRadius.medium};\n  transition: all 0.3s ease;\n  cursor: pointer;\n  opacity: ${props => props.isActive ? 1 : props.isCompleted ? 0.8 : 0.5};\n  \n  &:hover {\n    background-color: rgba(138, 112, 255, 0.05);\n    opacity: ${props => props.isActive ? 1 : 0.7};\n  }\n  \n  animation: ${props => props.isActive ? pulse : 'none'} 2s infinite ease-in-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 1.5rem;\n    top: 1.5rem;\n    width: 1rem;\n    height: 1rem;\n    border-radius: 50%;\n    background-color: ${props => props.isActive ? theme.colors.secondary : props.isCompleted ? 'rgba(138, 112, 255, 0.6)' : theme.colors.backgroundMedium};\n    transition: background-color 0.3s;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    left: 2rem;\n    top: ${props => props.isActive ? '2.5rem' : '1.5rem'};\n    width: 1px;\n    height: ${props => props.isActive ? 'calc(100% - 1rem)' : '100%'};\n    background-color: ${props => props.isActive || props.isCompleted ? 'rgba(138, 112, 255, 0.4)' : theme.colors.backgroundMedium};\n    transition: background-color 0.3s;\n  }\n  \n  &:last-child::after {\n    display: none;\n  }\n`;\n_c1 = TimelineItem;\nconst ItemTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => props.isActive ? theme.colors.secondary : theme.colors.text};\n  margin-bottom: 0.5rem;\n  transition: color 0.3s;\n`;\n_c10 = ItemTitle;\nconst ItemDescription = styled.p`\n  font-size: 0.9rem;\n  line-height: 1.5;\n  color: ${props => props.isActive ? theme.colors.text : theme.colors.textSecondary};\n  margin: 0;\n  transition: color 0.3s;\n  max-height: ${props => props.isActive ? '100px' : '0'};\n  overflow: hidden;\n  opacity: ${props => props.isActive ? 1 : 0};\n  transition: max-height 0.3s, opacity 0.3s;\n`;\n\n// Image component\n_c11 = ItemDescription;\nconst ExplorerImage = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: ${props => props.isActive ? 1 : 0};\n  transition: opacity 0.5s ease;\n  border-radius: ${theme.borderRadius.medium};\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n`;\n_c12 = ExplorerImage;\nconst Image = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n`;\n_c13 = Image;\nconst ErrorOverlay = styled.div`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: rgba(220, 38, 38, 0.9);\n  color: white;\n  font-size: 12px;\n  padding: 4px 10px;\n  border-radius: 4px;\n  font-family: monospace;\n`;\n_c14 = ErrorOverlay;\nconst ScrollHint = styled.div`\n  position: absolute;\n  bottom: 2rem;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: rgba(138, 112, 255, 0.8);\n  backdrop-filter: blur(4px);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 2rem;\n  font-size: 0.8rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  animation: ${fadeIn} 0.5s ease-out, ${bounce} 1.5s infinite alternate;\n  z-index: 90;\n  \n  &::after {\n    content: '↓';\n    font-size: 1rem;\n  }\n`;\n_c15 = ScrollHint;\nconst timelineData = [{\n  id: 1,\n  title: 'Error & Stack Trace',\n  description: 'Identify the exact location and cause of a bug with a detailed stack trace and human-readable error messages.',\n  image: explorerImage1,\n  hasError: true\n}, {\n  id: 2,\n  title: 'TX Details',\n  description: 'Explore transaction data with decoded parameters and real-time status updates.',\n  image: explorerImage2,\n  hasError: false\n}, {\n  id: 3,\n  title: 'Debugger',\n  description: 'Step through contract execution with an intuitive debugger that shows state changes and variable values.',\n  image: explorerImage3,\n  hasError: false\n}, {\n  id: 4,\n  title: 'Simulator',\n  description: 'Test transactions in a sandboxed environment before deploying to mainnet.',\n  image: explorerImage4,\n  hasError: false\n}, {\n  id: 5,\n  title: 'Gas Profiler',\n  description: 'Optimize your contracts with detailed gas usage statistics and improvement suggestions.',\n  image: explorerImage5,\n  hasError: false\n}];\nconst Explorer = () => {\n  _s();\n  const [activeIndex, setActiveIndex] = useState(0);\n  const [visitedItems, setVisitedItems] = useState(new Set([0]));\n  const [showScrollHint, setShowScrollHint] = useState(true);\n  const sectionRef = useRef(null);\n  const isScrolling = useRef(false);\n\n  // Handle scroll-like navigation\n  useEffect(() => {\n    const handleWheel = e => {\n      // Only handle wheel events when the section is visible\n      const section = sectionRef.current;\n      if (!section) return;\n      const rect = section.getBoundingClientRect();\n      const isInView = rect.top < window.innerHeight * 0.7 && rect.bottom > window.innerHeight * 0.3;\n      if (!isInView) return;\n\n      // Prevent rapid scrolling\n      if (isScrolling.current) return;\n      isScrolling.current = true;\n      const direction = e.deltaY > 0 ? 1 : -1;\n\n      // If we're at the last item and trying to go forward, let the normal scroll happen\n      if (direction > 0 && activeIndex === timelineData.length - 1) {\n        setShowScrollHint(false);\n        isScrolling.current = false;\n        return;\n      }\n\n      // If we're at the first item and trying to go backward, let the normal scroll happen\n      if (direction < 0 && activeIndex === 0) {\n        isScrolling.current = false;\n        return;\n      }\n\n      // Otherwise, change the active item\n      const newIndex = Math.max(0, Math.min(timelineData.length - 1, activeIndex + direction));\n      if (newIndex !== activeIndex) {\n        setActiveIndex(newIndex);\n        setVisitedItems(prev => new Set([...Array.from(prev), newIndex]));\n\n        // Small enhancement: prevent page scroll when changing items within the section\n        // This makes it feel more like scroll jacking without breaking the page\n        if (Math.abs(rect.top) < window.innerHeight * 0.8) {\n          e.preventDefault();\n        }\n      }\n\n      // Reset the scrolling state after a short delay\n      setTimeout(() => {\n        isScrolling.current = false;\n      }, 300);\n    };\n\n    // Use a passive listener that only prevents scrolling conditionally\n    window.addEventListener('wheel', handleWheel, {\n      passive: false\n    });\n    return () => {\n      window.removeEventListener('wheel', handleWheel);\n    };\n  }, [activeIndex]);\n\n  // Optional: Keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = e => {\n      const section = sectionRef.current;\n      if (!section) return;\n      const rect = section.getBoundingClientRect();\n      const isInView = rect.top < window.innerHeight * 0.7 && rect.bottom > window.innerHeight * 0.3;\n      if (!isInView) return;\n      if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {\n        if (activeIndex < timelineData.length - 1) {\n          setActiveIndex(activeIndex + 1);\n          setVisitedItems(prev => new Set([...Array.from(prev), activeIndex + 1]));\n        } else {\n          setShowScrollHint(false);\n        }\n      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {\n        if (activeIndex > 0) {\n          setActiveIndex(activeIndex - 1);\n          setVisitedItems(prev => new Set([...Array.from(prev), activeIndex - 1]));\n        }\n      }\n    };\n    window.addEventListener('keydown', handleKeyDown);\n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [activeIndex]);\n\n  // Handle item click\n  const handleItemClick = index => {\n    setActiveIndex(index);\n    setVisitedItems(prev => new Set([...Array.from(prev), index]));\n  };\n\n  // Handle dot click\n  const handleDotClick = index => {\n    setActiveIndex(index);\n    setVisitedItems(prev => new Set([...Array.from(prev), index]));\n  };\n  return /*#__PURE__*/_jsxDEV(ExplorerSectionContainer, {\n    ref: sectionRef,\n    children: [/*#__PURE__*/_jsxDEV(ContentContainer, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: [\"Developer \", /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Explorer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 28\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Explore while developing with the only multichain explorer that provides decoded, human-readable insights relevant to your dapp.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ExplorerContent, {\n        children: [/*#__PURE__*/_jsxDEV(TimelineContainer, {\n          children: timelineData.map((item, index) => /*#__PURE__*/_jsxDEV(TimelineItem, {\n            isActive: index === activeIndex,\n            isCompleted: visitedItems.has(index) && index !== activeIndex,\n            onClick: () => handleItemClick(index),\n            children: [/*#__PURE__*/_jsxDEV(ItemTitle, {\n              isActive: index === activeIndex,\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ItemDescription, {\n              isActive: index === activeIndex,\n              children: item.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ImagesContainer, {\n          children: timelineData.map((item, index) => /*#__PURE__*/_jsxDEV(ExplorerImage, {\n            isActive: index === activeIndex,\n            children: [/*#__PURE__*/_jsxDEV(Image, {\n              src: item.image,\n              alt: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), item.hasError && /*#__PURE__*/_jsxDEV(ErrorOverlay, {\n              children: \"Error: at line 315\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this)]\n          }, item.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ProgressDots, {\n        children: timelineData.map((_, index) => /*#__PURE__*/_jsxDEV(Dot, {\n          isActive: index === activeIndex,\n          onClick: () => handleDotClick(index),\n          \"aria-label\": `View ${timelineData[index].title}`\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 453,\n      columnNumber: 7\n    }, this), showScrollHint && activeIndex === timelineData.length - 1 && /*#__PURE__*/_jsxDEV(ScrollHint, {\n      children: \"Scroll to continue\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 452,\n    columnNumber: 5\n  }, this);\n};\n_s(Explorer, \"X5Gt2ADfOLja+2LHwSDF1fDK+gY=\");\n_c16 = Explorer;\nexport default Explorer;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"ExplorerSectionContainer\");\n$RefreshReg$(_c2, \"ContentContainer\");\n$RefreshReg$(_c3, \"SectionHeader\");\n$RefreshReg$(_c4, \"Title\");\n$RefreshReg$(_c5, \"Subtitle\");\n$RefreshReg$(_c6, \"ExplorerContent\");\n$RefreshReg$(_c7, \"TimelineContainer\");\n$RefreshReg$(_c8, \"ImagesContainer\");\n$RefreshReg$(_c9, \"ProgressDots\");\n$RefreshReg$(_c0, \"Dot\");\n$RefreshReg$(_c1, \"TimelineItem\");\n$RefreshReg$(_c10, \"ItemTitle\");\n$RefreshReg$(_c11, \"ItemDescription\");\n$RefreshReg$(_c12, \"ExplorerImage\");\n$RefreshReg$(_c13, \"Image\");\n$RefreshReg$(_c14, \"ErrorOverlay\");\n$RefreshReg$(_c15, \"ScrollHint\");\n$RefreshReg$(_c16, \"Explorer\");", "map": {"version": 3, "names": ["React", "useRef", "useEffect", "useState", "styled", "keyframes", "theme", "explorerImage1", "explorerImage2", "explorerImage3", "explorerImage4", "explorerImage5", "jsxDEV", "_jsxDEV", "fadeIn", "fadeInLeft", "fadeInRight", "pulse", "bounce", "ExplorerSectionContainer", "section", "colors", "background", "_c", "ContentContainer", "div", "_c2", "SectionHeader", "_c3", "Title", "h2", "text", "secondary", "_c4", "Subtitle", "p", "textSecondary", "_c5", "ExplorerContent", "breakpoints", "md", "_c6", "TimelineContainer", "_c7", "ImagesContainer", "_c8", "ProgressDots", "_c9", "Dot", "button", "props", "isActive", "backgroundMedium", "_c0", "TimelineItem", "borderRadius", "medium", "isCompleted", "_c1", "ItemTitle", "h3", "_c10", "ItemDescription", "_c11", "ExplorerImage", "_c12", "Image", "img", "_c13", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c14", "ScrollHint", "_c15", "timelineData", "id", "title", "description", "image", "<PERSON><PERSON><PERSON><PERSON>", "Explorer", "_s", "activeIndex", "setActiveIndex", "visitedItems", "setVisitedItems", "Set", "showScrollHint", "setShowScrollHint", "sectionRef", "isScrolling", "handleWheel", "e", "current", "rect", "getBoundingClientRect", "isInView", "top", "window", "innerHeight", "bottom", "direction", "deltaY", "length", "newIndex", "Math", "max", "min", "prev", "Array", "from", "abs", "preventDefault", "setTimeout", "addEventListener", "passive", "removeEventListener", "handleKeyDown", "key", "handleItemClick", "index", "handleDotClick", "ref", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "has", "onClick", "src", "alt", "_", "_c16", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Explorer/index.tsx"], "sourcesContent": ["import React, { useRef, useEffect, useState } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\n// Import images from assets\nimport explorerImage1 from '../../assets/home-explorer-1-mob.webp';\nimport explorerImage2 from '../../assets/home-explorer-2-mob.webp';\nimport explorerImage3 from '../../assets/home-explorer-3.webp';\nimport explorerImage4 from '../../assets/home-explorer-4.webp';\nimport explorerImage5 from '../../assets/home-explorer-5-mob.webp';\n\n// Animation keyframes\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\n\nconst fadeInLeft = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`;\n\nconst fadeInRight = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n`;\n\nconst bounce = keyframes`\n  from {\n    transform: translateY(0);\n  }\n  to {\n    transform: translateY(5px);\n  }\n`;\n\n// Styled components\nconst ExplorerSectionContainer = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  padding: 4rem 0;\n  position: relative;\n  background-color: ${theme.colors.background};\n`;\n\nconst ContentContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  position: relative;\n  z-index: 2;\n`;\n\nconst SectionHeader = styled.div`\n  text-align: center;\n  margin-bottom: 3rem;\n  animation: ${fadeIn} 0.8s forwards ease-out;\n`;\n\nconst Title = styled.h2`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1rem;\n  line-height: 1.2;\n  \n  span {\n    color: ${theme.colors.secondary};\n    display: inline-block;\n  }\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n`;\n\nconst ExplorerContent = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1.5fr;\n  gap: 3rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n    gap: 2rem;\n  }\n`;\n\nconst TimelineContainer = styled.div`\n  position: sticky;\n  top: 120px;\n  height: calc(100vh - 300px);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  animation: ${fadeInLeft} 1s forwards;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    position: relative;\n    top: 0;\n    height: auto;\n    margin-bottom: 2rem;\n  }\n`;\n\nconst ImagesContainer = styled.div`\n  position: relative;\n  min-height: 400px;\n  animation: ${fadeInRight} 1s forwards;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    min-height: 300px;\n  }\n`;\n\nconst ProgressDots = styled.div`\n  display: flex;\n  justify-content: center;\n  gap: 0.5rem;\n  margin-top: 1.5rem;\n`;\n\nconst Dot = styled.button<{ isActive: boolean }>`\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  background-color: ${props => props.isActive ? theme.colors.secondary : theme.colors.backgroundMedium};\n  border: none;\n  padding: 0;\n  cursor: pointer;\n  transition: transform 0.2s, background-color 0.2s;\n\n  &:hover {\n    transform: scale(1.2);\n  }\n`;\n\n// Timeline Item components\nconst TimelineItem = styled.div<{ isActive: boolean; isCompleted: boolean }>`\n  padding: 1.5rem;\n  position: relative;\n  padding-left: 3rem;\n  margin-bottom: 0.5rem;\n  background-color: ${props => props.isActive ? 'rgba(138, 112, 255, 0.1)' : 'transparent'};\n  border-radius: ${theme.borderRadius.medium};\n  transition: all 0.3s ease;\n  cursor: pointer;\n  opacity: ${props => props.isActive ? 1 : props.isCompleted ? 0.8 : 0.5};\n  \n  &:hover {\n    background-color: rgba(138, 112, 255, 0.05);\n    opacity: ${props => props.isActive ? 1 : 0.7};\n  }\n  \n  animation: ${props => props.isActive ? pulse : 'none'} 2s infinite ease-in-out;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    left: 1.5rem;\n    top: 1.5rem;\n    width: 1rem;\n    height: 1rem;\n    border-radius: 50%;\n    background-color: ${props => \n      props.isActive \n        ? theme.colors.secondary \n        : props.isCompleted \n          ? 'rgba(138, 112, 255, 0.6)'\n          : theme.colors.backgroundMedium\n    };\n    transition: background-color 0.3s;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    left: 2rem;\n    top: ${props => props.isActive ? '2.5rem' : '1.5rem'};\n    width: 1px;\n    height: ${props => props.isActive ? 'calc(100% - 1rem)' : '100%'};\n    background-color: ${props => \n      props.isActive || props.isCompleted\n        ? 'rgba(138, 112, 255, 0.4)'\n        : theme.colors.backgroundMedium\n    };\n    transition: background-color 0.3s;\n  }\n  \n  &:last-child::after {\n    display: none;\n  }\n`;\n\nconst ItemTitle = styled.h3<{ isActive: boolean }>`\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: ${props => props.isActive ? theme.colors.secondary : theme.colors.text};\n  margin-bottom: 0.5rem;\n  transition: color 0.3s;\n`;\n\nconst ItemDescription = styled.p<{ isActive: boolean }>`\n  font-size: 0.9rem;\n  line-height: 1.5;\n  color: ${props => props.isActive ? theme.colors.text : theme.colors.textSecondary};\n  margin: 0;\n  transition: color 0.3s;\n  max-height: ${props => props.isActive ? '100px' : '0'};\n  overflow: hidden;\n  opacity: ${props => props.isActive ? 1 : 0};\n  transition: max-height 0.3s, opacity 0.3s;\n`;\n\n// Image component\nconst ExplorerImage = styled.div<{ isActive: boolean }>`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: ${props => props.isActive ? 1 : 0};\n  transition: opacity 0.5s ease;\n  border-radius: ${theme.borderRadius.medium};\n  overflow: hidden;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n`;\n\nconst Image = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: block;\n`;\n\nconst ErrorOverlay = styled.div`\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  background-color: rgba(220, 38, 38, 0.9);\n  color: white;\n  font-size: 12px;\n  padding: 4px 10px;\n  border-radius: 4px;\n  font-family: monospace;\n`;\n\nconst ScrollHint = styled.div`\n  position: absolute;\n  bottom: 2rem;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: rgba(138, 112, 255, 0.8);\n  backdrop-filter: blur(4px);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 2rem;\n  font-size: 0.8rem;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  animation: ${fadeIn} 0.5s ease-out, ${bounce} 1.5s infinite alternate;\n  z-index: 90;\n  \n  &::after {\n    content: '↓';\n    font-size: 1rem;\n  }\n`;\n\nconst timelineData = [\n  {\n    id: 1,\n    title: 'Error & Stack Trace',\n    description: 'Identify the exact location and cause of a bug with a detailed stack trace and human-readable error messages.',\n    image: explorerImage1,\n    hasError: true\n  },\n  {\n    id: 2,\n    title: 'TX Details',\n    description: 'Explore transaction data with decoded parameters and real-time status updates.',\n    image: explorerImage2,\n    hasError: false\n  },\n  {\n    id: 3,\n    title: 'Debugger',\n    description: 'Step through contract execution with an intuitive debugger that shows state changes and variable values.',\n    image: explorerImage3,\n    hasError: false\n  },\n  {\n    id: 4,\n    title: 'Simulator',\n    description: 'Test transactions in a sandboxed environment before deploying to mainnet.',\n    image: explorerImage4,\n    hasError: false\n  },\n  {\n    id: 5,\n    title: 'Gas Profiler',\n    description: 'Optimize your contracts with detailed gas usage statistics and improvement suggestions.',\n    image: explorerImage5,\n    hasError: false\n  }\n];\n\nconst Explorer: React.FC = () => {\n  const [activeIndex, setActiveIndex] = useState(0);\n  const [visitedItems, setVisitedItems] = useState<Set<number>>(new Set([0]));\n  const [showScrollHint, setShowScrollHint] = useState(true);\n  const sectionRef = useRef<HTMLElement>(null);\n  const isScrolling = useRef(false);\n  \n  // Handle scroll-like navigation\n  useEffect(() => {\n    const handleWheel = (e: WheelEvent) => {\n      // Only handle wheel events when the section is visible\n      const section = sectionRef.current;\n      if (!section) return;\n      \n      const rect = section.getBoundingClientRect();\n      const isInView = rect.top < window.innerHeight * 0.7 && rect.bottom > window.innerHeight * 0.3;\n      \n      if (!isInView) return;\n      \n      // Prevent rapid scrolling\n      if (isScrolling.current) return;\n      isScrolling.current = true;\n      \n      const direction = e.deltaY > 0 ? 1 : -1;\n      \n      // If we're at the last item and trying to go forward, let the normal scroll happen\n      if (direction > 0 && activeIndex === timelineData.length - 1) {\n        setShowScrollHint(false);\n        isScrolling.current = false;\n        return;\n      }\n      \n      // If we're at the first item and trying to go backward, let the normal scroll happen\n      if (direction < 0 && activeIndex === 0) {\n        isScrolling.current = false;\n        return;\n      }\n      \n      // Otherwise, change the active item\n      const newIndex = Math.max(0, Math.min(timelineData.length - 1, activeIndex + direction));\n      \n      if (newIndex !== activeIndex) {\n        setActiveIndex(newIndex);\n        setVisitedItems(prev => new Set([...Array.from(prev), newIndex]));\n        \n        // Small enhancement: prevent page scroll when changing items within the section\n        // This makes it feel more like scroll jacking without breaking the page\n        if (Math.abs(rect.top) < window.innerHeight * 0.8) {\n          e.preventDefault();\n        }\n      }\n      \n      // Reset the scrolling state after a short delay\n      setTimeout(() => {\n        isScrolling.current = false;\n      }, 300);\n    };\n    \n    // Use a passive listener that only prevents scrolling conditionally\n    window.addEventListener('wheel', handleWheel, { passive: false });\n    \n    return () => {\n      window.removeEventListener('wheel', handleWheel);\n    };\n  }, [activeIndex]);\n  \n  // Optional: Keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      const section = sectionRef.current;\n      if (!section) return;\n      \n      const rect = section.getBoundingClientRect();\n      const isInView = rect.top < window.innerHeight * 0.7 && rect.bottom > window.innerHeight * 0.3;\n      \n      if (!isInView) return;\n      \n      if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {\n        if (activeIndex < timelineData.length - 1) {\n          setActiveIndex(activeIndex + 1);\n          setVisitedItems(prev => new Set([...Array.from(prev), activeIndex + 1]));\n        } else {\n          setShowScrollHint(false);\n        }\n      } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {\n        if (activeIndex > 0) {\n          setActiveIndex(activeIndex - 1);\n          setVisitedItems(prev => new Set([...Array.from(prev), activeIndex - 1]));\n        }\n      }\n    };\n    \n    window.addEventListener('keydown', handleKeyDown);\n    \n    return () => {\n      window.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [activeIndex]);\n  \n  // Handle item click\n  const handleItemClick = (index: number) => {\n    setActiveIndex(index);\n    setVisitedItems(prev => new Set([...Array.from(prev), index]));\n  };\n  \n  // Handle dot click\n  const handleDotClick = (index: number) => {\n    setActiveIndex(index);\n    setVisitedItems(prev => new Set([...Array.from(prev), index]));\n  };\n  \n  return (\n    <ExplorerSectionContainer ref={sectionRef}>\n      <ContentContainer>\n        <SectionHeader>\n          <Title>Developer <span>Explorer</span></Title>\n          <Subtitle>\n            Explore while developing with the only multichain explorer that provides decoded, human-readable insights relevant to your dapp.\n          </Subtitle>\n        </SectionHeader>\n        \n        <ExplorerContent>\n          <TimelineContainer>\n            {timelineData.map((item, index) => (\n              <TimelineItem \n                key={item.id}\n                isActive={index === activeIndex}\n                isCompleted={visitedItems.has(index) && index !== activeIndex}\n                onClick={() => handleItemClick(index)}\n              >\n                <ItemTitle isActive={index === activeIndex}>{item.title}</ItemTitle>\n                <ItemDescription isActive={index === activeIndex}>\n                  {item.description}\n                </ItemDescription>\n              </TimelineItem>\n            ))}\n          </TimelineContainer>\n          \n          <ImagesContainer>\n            {timelineData.map((item, index) => (\n              <ExplorerImage\n                key={item.id}\n                isActive={index === activeIndex}\n              >\n                <Image src={item.image} alt={item.title} />\n                {item.hasError && (\n                  <ErrorOverlay>Error: at line 315</ErrorOverlay>\n                )}\n              </ExplorerImage>\n            ))}\n          </ImagesContainer>\n        </ExplorerContent>\n        \n        <ProgressDots>\n          {timelineData.map((_, index) => (\n            <Dot \n              key={index}\n              isActive={index === activeIndex}\n              onClick={() => handleDotClick(index)}\n              aria-label={`View ${timelineData[index].title}`}\n            />\n          ))}\n        </ProgressDots>\n      </ContentContainer>\n      \n      {showScrollHint && activeIndex === timelineData.length - 1 && (\n        <ScrollHint>\n          Scroll to continue\n        </ScrollHint>\n      )}\n    </ExplorerSectionContainer>\n  );\n};\n\nexport default Explorer; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,KAAK,QAAQ,oBAAoB;;AAE1C;AACA,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,uCAAuC;AAClE,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,cAAc,MAAM,mCAAmC;AAC9D,OAAOC,cAAc,MAAM,uCAAuC;;AAElE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAGT,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMU,UAAU,GAAGV,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMW,WAAW,GAAGX,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMY,KAAK,GAAGZ,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMa,MAAM,GAAGb,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA,MAAMc,wBAAwB,GAAGf,MAAM,CAACgB,OAAO;AAC/C;AACA;AACA;AACA;AACA,sBAAsBd,KAAK,CAACe,MAAM,CAACC,UAAU;AAC7C,CAAC;AAACC,EAAA,GANIJ,wBAAwB;AAQ9B,MAAMK,gBAAgB,GAAGpB,MAAM,CAACqB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,gBAAgB;AAQtB,MAAMG,aAAa,GAAGvB,MAAM,CAACqB,GAAG;AAChC;AACA;AACA,eAAeX,MAAM;AACrB,CAAC;AAACc,GAAA,GAJID,aAAa;AAMnB,MAAME,KAAK,GAAGzB,MAAM,CAAC0B,EAAE;AACvB;AACA;AACA,WAAWxB,KAAK,CAACe,MAAM,CAACU,IAAI;AAC5B;AACA;AACA;AACA;AACA,aAAazB,KAAK,CAACe,MAAM,CAACW,SAAS;AACnC;AACA;AACA,CAAC;AAACC,GAAA,GAXIJ,KAAK;AAaX,MAAMK,QAAQ,GAAG9B,MAAM,CAAC+B,CAAC;AACzB;AACA,WAAW7B,KAAK,CAACe,MAAM,CAACe,aAAa;AACrC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,QAAQ;AAQd,MAAMI,eAAe,GAAGlC,MAAM,CAACqB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACiC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIH,eAAe;AAYrB,MAAMI,iBAAiB,GAAGtC,MAAM,CAACqB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,eAAeV,UAAU;AACzB;AACA,uBAAuBT,KAAK,CAACiC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAfID,iBAAiB;AAiBvB,MAAME,eAAe,GAAGxC,MAAM,CAACqB,GAAG;AAClC;AACA;AACA,eAAeT,WAAW;AAC1B;AACA,uBAAuBV,KAAK,CAACiC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACK,GAAA,GARID,eAAe;AAUrB,MAAME,YAAY,GAAG1C,MAAM,CAACqB,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GALID,YAAY;AAOlB,MAAME,GAAG,GAAG5C,MAAM,CAAC6C,MAA6B;AAChD;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG7C,KAAK,CAACe,MAAM,CAACW,SAAS,GAAG1B,KAAK,CAACe,MAAM,CAAC+B,gBAAgB;AACtG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAC,GAAA,GAfML,GAAG;AAgBT,MAAMM,YAAY,GAAGlD,MAAM,CAACqB,GAAgD;AAC5E;AACA;AACA;AACA;AACA,sBAAsByB,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,0BAA0B,GAAG,aAAa;AAC1F,mBAAmB7C,KAAK,CAACiD,YAAY,CAACC,MAAM;AAC5C;AACA;AACA,aAAaN,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,CAAC,GAAGD,KAAK,CAACO,WAAW,GAAG,GAAG,GAAG,GAAG;AACxE;AACA;AACA;AACA,eAAeP,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,CAAC,GAAG,GAAG;AAChD;AACA;AACA,eAAeD,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAGlC,KAAK,GAAG,MAAM;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBiC,KAAK,IACvBA,KAAK,CAACC,QAAQ,GACV7C,KAAK,CAACe,MAAM,CAACW,SAAS,GACtBkB,KAAK,CAACO,WAAW,GACf,0BAA0B,GAC1BnD,KAAK,CAACe,MAAM,CAAC+B,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WACWF,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACxD;AACA,cAAcD,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,mBAAmB,GAAG,MAAM;AACpE,wBAAwBD,KAAK,IACvBA,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACO,WAAW,GAC/B,0BAA0B,GAC1BnD,KAAK,CAACe,MAAM,CAAC+B,gBAAgB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,CACC;AAACM,GAAA,GAtDIJ,YAAY;AAwDlB,MAAMK,SAAS,GAAGvD,MAAM,CAACwD,EAAyB;AAClD;AACA;AACA,WAAWV,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG7C,KAAK,CAACe,MAAM,CAACW,SAAS,GAAG1B,KAAK,CAACe,MAAM,CAACU,IAAI;AAC/E;AACA;AACA,CAAC;AAAC8B,IAAA,GANIF,SAAS;AAQf,MAAMG,eAAe,GAAG1D,MAAM,CAAC+B,CAAwB;AACvD;AACA;AACA,WAAWe,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG7C,KAAK,CAACe,MAAM,CAACU,IAAI,GAAGzB,KAAK,CAACe,MAAM,CAACe,aAAa;AACnF;AACA;AACA,gBAAgBc,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,OAAO,GAAG,GAAG;AACvD;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,CAAC,GAAG,CAAC;AAC5C;AACA,CAAC;;AAED;AAAAY,IAAA,GAZMD,eAAe;AAarB,MAAME,aAAa,GAAG5D,MAAM,CAACqB,GAA0B;AACvD;AACA;AACA;AACA;AACA;AACA,aAAayB,KAAK,IAAIA,KAAK,CAACC,QAAQ,GAAG,CAAC,GAAG,CAAC;AAC5C;AACA,mBAAmB7C,KAAK,CAACiD,YAAY,CAACC,MAAM;AAC5C;AACA;AACA,CAAC;AAACS,IAAA,GAXID,aAAa;AAanB,MAAME,KAAK,GAAG9D,MAAM,CAAC+D,GAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GALIF,KAAK;AAOX,MAAMG,YAAY,GAAGjE,MAAM,CAACqB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6C,IAAA,GAVID,YAAY;AAYlB,MAAME,UAAU,GAAGnE,MAAM,CAACqB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeX,MAAM,mBAAmBI,MAAM;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsD,IAAA,GArBID,UAAU;AAuBhB,MAAME,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EAAE,+GAA+G;EAC5HC,KAAK,EAAEtE,cAAc;EACrBuE,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,YAAY;EACnBC,WAAW,EAAE,gFAAgF;EAC7FC,KAAK,EAAErE,cAAc;EACrBsE,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,UAAU;EACjBC,WAAW,EAAE,0GAA0G;EACvHC,KAAK,EAAEpE,cAAc;EACrBqE,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,2EAA2E;EACxFC,KAAK,EAAEnE,cAAc;EACrBoE,QAAQ,EAAE;AACZ,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,cAAc;EACrBC,WAAW,EAAE,yFAAyF;EACtGC,KAAK,EAAElE,cAAc;EACrBmE,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAc,IAAIkF,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAMqF,UAAU,GAAGvF,MAAM,CAAc,IAAI,CAAC;EAC5C,MAAMwF,WAAW,GAAGxF,MAAM,CAAC,KAAK,CAAC;;EAEjC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwF,WAAW,GAAIC,CAAa,IAAK;MACrC;MACA,MAAMvE,OAAO,GAAGoE,UAAU,CAACI,OAAO;MAClC,IAAI,CAACxE,OAAO,EAAE;MAEd,MAAMyE,IAAI,GAAGzE,OAAO,CAAC0E,qBAAqB,CAAC,CAAC;MAC5C,MAAMC,QAAQ,GAAGF,IAAI,CAACG,GAAG,GAAGC,MAAM,CAACC,WAAW,GAAG,GAAG,IAAIL,IAAI,CAACM,MAAM,GAAGF,MAAM,CAACC,WAAW,GAAG,GAAG;MAE9F,IAAI,CAACH,QAAQ,EAAE;;MAEf;MACA,IAAIN,WAAW,CAACG,OAAO,EAAE;MACzBH,WAAW,CAACG,OAAO,GAAG,IAAI;MAE1B,MAAMQ,SAAS,GAAGT,CAAC,CAACU,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;MAEvC;MACA,IAAID,SAAS,GAAG,CAAC,IAAInB,WAAW,KAAKR,YAAY,CAAC6B,MAAM,GAAG,CAAC,EAAE;QAC5Df,iBAAiB,CAAC,KAAK,CAAC;QACxBE,WAAW,CAACG,OAAO,GAAG,KAAK;QAC3B;MACF;;MAEA;MACA,IAAIQ,SAAS,GAAG,CAAC,IAAInB,WAAW,KAAK,CAAC,EAAE;QACtCQ,WAAW,CAACG,OAAO,GAAG,KAAK;QAC3B;MACF;;MAEA;MACA,MAAMW,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACjC,YAAY,CAAC6B,MAAM,GAAG,CAAC,EAAErB,WAAW,GAAGmB,SAAS,CAAC,CAAC;MAExF,IAAIG,QAAQ,KAAKtB,WAAW,EAAE;QAC5BC,cAAc,CAACqB,QAAQ,CAAC;QACxBnB,eAAe,CAACuB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC,EAAEJ,QAAQ,CAAC,CAAC,CAAC;;QAEjE;QACA;QACA,IAAIC,IAAI,CAACM,GAAG,CAACjB,IAAI,CAACG,GAAG,CAAC,GAAGC,MAAM,CAACC,WAAW,GAAG,GAAG,EAAE;UACjDP,CAAC,CAACoB,cAAc,CAAC,CAAC;QACpB;MACF;;MAEA;MACAC,UAAU,CAAC,MAAM;QACfvB,WAAW,CAACG,OAAO,GAAG,KAAK;MAC7B,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;;IAED;IACAK,MAAM,CAACgB,gBAAgB,CAAC,OAAO,EAAEvB,WAAW,EAAE;MAAEwB,OAAO,EAAE;IAAM,CAAC,CAAC;IAEjE,OAAO,MAAM;MACXjB,MAAM,CAACkB,mBAAmB,CAAC,OAAO,EAAEzB,WAAW,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;;EAEjB;EACA/E,SAAS,CAAC,MAAM;IACd,MAAMkH,aAAa,GAAIzB,CAAgB,IAAK;MAC1C,MAAMvE,OAAO,GAAGoE,UAAU,CAACI,OAAO;MAClC,IAAI,CAACxE,OAAO,EAAE;MAEd,MAAMyE,IAAI,GAAGzE,OAAO,CAAC0E,qBAAqB,CAAC,CAAC;MAC5C,MAAMC,QAAQ,GAAGF,IAAI,CAACG,GAAG,GAAGC,MAAM,CAACC,WAAW,GAAG,GAAG,IAAIL,IAAI,CAACM,MAAM,GAAGF,MAAM,CAACC,WAAW,GAAG,GAAG;MAE9F,IAAI,CAACH,QAAQ,EAAE;MAEf,IAAIJ,CAAC,CAAC0B,GAAG,KAAK,YAAY,IAAI1B,CAAC,CAAC0B,GAAG,KAAK,WAAW,EAAE;QACnD,IAAIpC,WAAW,GAAGR,YAAY,CAAC6B,MAAM,GAAG,CAAC,EAAE;UACzCpB,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;UAC/BG,eAAe,CAACuB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC,EAAE1B,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC,MAAM;UACLM,iBAAiB,CAAC,KAAK,CAAC;QAC1B;MACF,CAAC,MAAM,IAAII,CAAC,CAAC0B,GAAG,KAAK,WAAW,IAAI1B,CAAC,CAAC0B,GAAG,KAAK,SAAS,EAAE;QACvD,IAAIpC,WAAW,GAAG,CAAC,EAAE;UACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;UAC/BG,eAAe,CAACuB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC,EAAE1B,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1E;MACF;IACF,CAAC;IAEDgB,MAAM,CAACgB,gBAAgB,CAAC,SAAS,EAAEG,aAAa,CAAC;IAEjD,OAAO,MAAM;MACXnB,MAAM,CAACkB,mBAAmB,CAAC,SAAS,EAAEC,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAACnC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMqC,eAAe,GAAIC,KAAa,IAAK;IACzCrC,cAAc,CAACqC,KAAK,CAAC;IACrBnC,eAAe,CAACuB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC,EAAEY,KAAK,CAAC,CAAC,CAAC;EAChE,CAAC;;EAED;EACA,MAAMC,cAAc,GAAID,KAAa,IAAK;IACxCrC,cAAc,CAACqC,KAAK,CAAC;IACrBnC,eAAe,CAACuB,IAAI,IAAI,IAAItB,GAAG,CAAC,CAAC,GAAGuB,KAAK,CAACC,IAAI,CAACF,IAAI,CAAC,EAAEY,KAAK,CAAC,CAAC,CAAC;EAChE,CAAC;EAED,oBACE1G,OAAA,CAACM,wBAAwB;IAACsG,GAAG,EAAEjC,UAAW;IAAAkC,QAAA,gBACxC7G,OAAA,CAACW,gBAAgB;MAAAkG,QAAA,gBACf7G,OAAA,CAACc,aAAa;QAAA+F,QAAA,gBACZ7G,OAAA,CAACgB,KAAK;UAAA6F,QAAA,GAAC,YAAU,eAAA7G,OAAA;YAAA6G,QAAA,EAAM;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9CjH,OAAA,CAACqB,QAAQ;UAAAwF,QAAA,EAAC;QAEV;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEhBjH,OAAA,CAACyB,eAAe;QAAAoF,QAAA,gBACd7G,OAAA,CAAC6B,iBAAiB;UAAAgF,QAAA,EACfjD,YAAY,CAACsD,GAAG,CAAC,CAACC,IAAI,EAAET,KAAK,kBAC5B1G,OAAA,CAACyC,YAAY;YAEXH,QAAQ,EAAEoE,KAAK,KAAKtC,WAAY;YAChCxB,WAAW,EAAE0B,YAAY,CAAC8C,GAAG,CAACV,KAAK,CAAC,IAAIA,KAAK,KAAKtC,WAAY;YAC9DiD,OAAO,EAAEA,CAAA,KAAMZ,eAAe,CAACC,KAAK,CAAE;YAAAG,QAAA,gBAEtC7G,OAAA,CAAC8C,SAAS;cAACR,QAAQ,EAAEoE,KAAK,KAAKtC,WAAY;cAAAyC,QAAA,EAAEM,IAAI,CAACrD;YAAK;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpEjH,OAAA,CAACiD,eAAe;cAACX,QAAQ,EAAEoE,KAAK,KAAKtC,WAAY;cAAAyC,QAAA,EAC9CM,IAAI,CAACpD;YAAW;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,GARbE,IAAI,CAACtD,EAAE;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASA,CACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACe,CAAC,eAEpBjH,OAAA,CAAC+B,eAAe;UAAA8E,QAAA,EACbjD,YAAY,CAACsD,GAAG,CAAC,CAACC,IAAI,EAAET,KAAK,kBAC5B1G,OAAA,CAACmD,aAAa;YAEZb,QAAQ,EAAEoE,KAAK,KAAKtC,WAAY;YAAAyC,QAAA,gBAEhC7G,OAAA,CAACqD,KAAK;cAACiE,GAAG,EAAEH,IAAI,CAACnD,KAAM;cAACuD,GAAG,EAAEJ,IAAI,CAACrD;YAAM;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CE,IAAI,CAAClD,QAAQ,iBACZjE,OAAA,CAACwD,YAAY;cAAAqD,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAc,CAC/C;UAAA,GANIE,IAAI,CAACtD,EAAE;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOC,CAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAElBjH,OAAA,CAACiC,YAAY;QAAA4E,QAAA,EACVjD,YAAY,CAACsD,GAAG,CAAC,CAACM,CAAC,EAAEd,KAAK,kBACzB1G,OAAA,CAACmC,GAAG;UAEFG,QAAQ,EAAEoE,KAAK,KAAKtC,WAAY;UAChCiD,OAAO,EAAEA,CAAA,KAAMV,cAAc,CAACD,KAAK,CAAE;UACrC,cAAY,QAAQ9C,YAAY,CAAC8C,KAAK,CAAC,CAAC5C,KAAK;QAAG,GAH3C4C,KAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAElBxC,cAAc,IAAIL,WAAW,KAAKR,YAAY,CAAC6B,MAAM,GAAG,CAAC,iBACxDzF,OAAA,CAAC0D,UAAU;MAAAmD,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACuB,CAAC;AAE/B,CAAC;AAAC9C,EAAA,CA5KID,QAAkB;AAAAuD,IAAA,GAAlBvD,QAAkB;AA8KxB,eAAeA,QAAQ;AAAC,IAAAxD,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAM,GAAA,EAAAK,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA8D,IAAA;AAAAC,YAAA,CAAAhH,EAAA;AAAAgH,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA3G,GAAA;AAAA2G,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAtE,IAAA;AAAAsE,YAAA,CAAAnE,IAAA;AAAAmE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}