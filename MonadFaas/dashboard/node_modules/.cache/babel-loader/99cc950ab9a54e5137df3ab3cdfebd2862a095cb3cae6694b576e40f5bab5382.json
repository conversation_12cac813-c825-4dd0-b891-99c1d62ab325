{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Planets/Planet.tsx\";\nimport React from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { theme } from '../../../styles/theme';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst float = keyframes`\n  0% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-15px) rotate(2deg);\n  }\n  100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n`;\nconst orbitRotate = keyframes`\n  from {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n  to {\n    transform: translate(-50%, -50%) rotate(360deg);\n  }\n`;\nconst secondaryOrbitRotate = keyframes`\n  from {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n  to {\n    transform: translate(-50%, -50%) rotate(-360deg);\n  }\n`;\nconst featurePointFloat = keyframes`\n  0% {\n    transform: translateY(0px) scale(1);\n    box-shadow: 0 0 15px currentColor;\n  }\n  50% {\n    transform: translateY(-5px) scale(1.1);\n    box-shadow: 0 0 25px currentColor;\n  }\n  100% {\n    transform: translateY(0px) scale(1);\n    box-shadow: 0 0 15px currentColor;\n  }\n`;\nconst shine = keyframes`\n  0% {\n    opacity: 0.3;\n  }\n  50% {\n    opacity: 0.7;\n  }\n  100% {\n    opacity: 0.3;\n  }\n`;\nconst orbitalShine = keyframes`\n  0%, 100% {\n    opacity: 0.2;\n  }\n  50% {\n    opacity: 0.8;\n  }\n`;\nconst textGlow = keyframes`\n  0%, 100% {\n    text-shadow: 0 0 20px rgba(255, 255, 255, 0.4);\n  }\n  50% {\n    text-shadow: 0 0 30px rgba(255, 255, 255, 0.7), 0 0 40px currentColor;\n  }\n`;\n// Define pulse animation with proper typing\nconst getPulseAnimation = (color = 'rgba(92, 69, 255, 0.4)') => keyframes`\n  0% {\n    filter: drop-shadow(0 0 20px ${color});\n    transform: scale(1);\n  }\n  50% {\n    filter: drop-shadow(0 0 60px ${color.replace('0.4', '0.6')});\n    transform: scale(1.05);\n  }\n  100% {\n    filter: drop-shadow(0 0 20px ${color});\n    transform: scale(1);\n  }\n`;\nconst PlanetContainer = styled.div`\n  position: relative;\n  width: ${props => props.size || '300px'};\n  height: ${props => props.size || '300px'};\n  z-index: ${props => props.zIndex || 1};\n  animation: ${float} 8s ease-in-out infinite;\n  animation-delay: ${props => props.delay || '0s'};\n  transition: all 0.5s ease-in-out;\n  \n  &::after {\n    content: '${props => props.planetType.toUpperCase()}';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    color: white;\n    font-size: 2.5rem;\n    font-weight: bold;\n    text-shadow: 0 0 20px rgba(0, 0, 0, 0.7);\n    z-index: 3;\n    letter-spacing: 2px;\n    animation: ${textGlow} 3s infinite ease-in-out;\n    color: ${props => props.planetType === 'build' ? '#FFFFFF' : '#FFFFFF'};\n  }\n  \n  ${props => props.active && css`\n    animation: ${float} 8s ease-in-out infinite, ${getPulseAnimation(props.color)} 3s ease-in-out infinite;\n    transform: scale(1.05);\n  `}\n`;\n_c = PlanetContainer;\nconst PlanetImage = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  filter: drop-shadow(0 0 20px rgba(92, 69, 255, 0.5));\n  transition: all 0.3s ease-in-out;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0) 70%);\n    mix-blend-mode: overlay;\n    animation: ${shine} 5s infinite;\n  }\n`;\n_c2 = PlanetImage;\nconst PlanetRing = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 110%;\n  height: 110%;\n  border-radius: 50%;\n  border: 2px solid ${props => props.color};\n  opacity: 0.3;\n  box-shadow: 0 0 15px ${props => props.color};\n  pointer-events: none;\n  z-index: 4;\n`;\n_c3 = PlanetRing;\nconst PlanetGlow = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 130%;\n  height: 130%;\n  border-radius: 50%;\n  background: radial-gradient(\n    circle,\n    ${props => props.color}40 0%,\n    ${props => props.color}20 30%,\n    ${props => props.color}10 60%,\n    transparent 70%\n  );\n  filter: blur(20px);\n  opacity: 0.7;\n  z-index: 0;\n  pointer-events: none;\n`;\n_c4 = PlanetGlow;\nconst OrbitPath = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: ${props => `calc(${props.size} * 1.5)`};\n  height: ${props => `calc(${props.size} * 1.5)`};\n  border: 1px solid ${props => props.color || 'rgba(92, 69, 255, 0.2)'};\n  border-radius: 50%;\n  z-index: 0;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -15px;\n    left: -15px;\n    right: -15px;\n    bottom: -15px;\n    border: 1px solid ${props => props.color ? props.color.replace('0.2', '0.1') : 'rgba(92, 69, 255, 0.1)'};\n    border-radius: 50%;\n    animation: ${orbitalShine} 8s infinite ease-in-out;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -30px;\n    left: -30px;\n    right: -30px;\n    bottom: -30px;\n    border: 0.5px solid ${props => props.color ? props.color.replace('0.2', '0.05') : 'rgba(92, 69, 255, 0.05)'};\n    border-radius: 50%;\n    animation: ${orbitalShine} 12s infinite ease-in-out reverse;\n  }\n`;\n_c5 = OrbitPath;\nconst Orbit = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: ${props => props.size};\n  height: ${props => props.size};\n  border: 1px solid ${props => props.color || 'rgba(92, 69, 255, 0.3)'};\n  border-radius: 50%;\n  z-index: 0;\n  transform: translate(-50%, -50%);\n  animation: ${props => props.reverse ? secondaryOrbitRotate : orbitRotate} ${props => props.duration || '30s'} linear infinite;\n  animation-delay: ${props => props.delay || '0s'};\n`;\n_c6 = Orbit;\nconst FeaturePoint = styled.div`\n  position: absolute;\n  width: 44px;\n  height: 44px;\n  border-radius: 50%;\n  background-color: ${props => props.color || theme.colors.primary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 0 15px ${props => props.color || theme.colors.primary};\n  animation: ${featurePointFloat} 4s ease-in-out infinite;\n  animation-delay: ${props => `${props.index * 0.5}s`};\n  z-index: 5;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  ${props => props.active && css`\n    transform: scale(1.2);\n    box-shadow: 0 0 25px ${props.color || theme.colors.primary};\n  `}\n  \n  svg {\n    width: 20px;\n    height: 20px;\n    color: white;\n    filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    left: -5px;\n    right: -5px;\n    bottom: -5px;\n    border-radius: 50%;\n    border: 1px solid ${props => props.color || theme.colors.primary};\n    opacity: 0.5;\n    animation: ${orbitalShine} 3s infinite;\n  }\n`;\n_c7 = FeaturePoint;\nconst ConnectionLine = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  height: 2px;\n  background-color: ${props => props.color};\n  transform-origin: left center;\n  box-shadow: 0 0 8px ${props => props.color};\n  opacity: 0.7;\n  pointer-events: none;\n`;\n\n// Calculate position on orbit\n_c8 = ConnectionLine;\nconst getPointPosition = (index, total, radius) => {\n  const angle = index / total * 2 * Math.PI;\n  const x = 50 + Math.cos(angle) * radius;\n  const y = 50 + Math.sin(angle) * radius;\n  return {\n    x,\n    y,\n    angle\n  };\n};\nconst OrbitFeaturePoint = ({\n  icon,\n  index,\n  totalPoints,\n  color,\n  active,\n  onClick\n}) => {\n  const position = getPointPosition(index, totalPoints, 45);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(FeaturePoint, {\n      style: {\n        left: `${position.x}%`,\n        top: `${position.y}%`,\n        transform: 'translate(-50%, -50%)'\n      },\n      color: color,\n      index: index,\n      active: active,\n      onClick: onClick,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this), active && /*#__PURE__*/_jsxDEV(ConnectionLine, {\n      color: color || theme.colors.primary,\n      style: {\n        left: '50%',\n        width: `${45}%`,\n        transform: `rotate(${position.angle}rad)`\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_c9 = OrbitFeaturePoint;\nconst Planet = ({\n  type,\n  size = \"300px\",\n  active = false,\n  showOrbit = true,\n  color = 'rgba(92, 69, 255, 0.4)',\n  delay,\n  zIndex,\n  orbitPoints = [],\n  activeFeatureIndex,\n  onFeatureClick\n}) => {\n  const planetSrc = type === 'build' ? '/planet1.webp' : '/planet2.webp';\n  const orbitColor = type === 'build' ? 'rgba(0, 210, 194, 0.2)' : 'rgba(138, 112, 255, 0.2)';\n  const baseColor = type === 'build' ? '#00d2c2' : '#8a70ff';\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [showOrbit && /*#__PURE__*/_jsxDEV(OrbitPath, {\n      size: size,\n      color: orbitColor\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 21\n    }, this), orbitPoints.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Orbit, {\n        size: `calc(${size} * 2.2)`,\n        duration: \"60s\",\n        color: orbitColor,\n        children: orbitPoints.map((icon, index) => /*#__PURE__*/_jsxDEV(OrbitFeaturePoint, {\n          icon: icon,\n          index: index,\n          totalPoints: orbitPoints.length,\n          color: baseColor,\n          active: activeFeatureIndex === index,\n          onClick: () => onFeatureClick && onFeatureClick(index)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Orbit, {\n        size: `calc(${size} * 1.8)`,\n        duration: \"40s\",\n        color: orbitColor,\n        reverse: true,\n        delay: \"5s\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(PlanetContainer, {\n      size: size,\n      active: active,\n      color: color,\n      delay: delay,\n      planetType: type,\n      zIndex: zIndex,\n      children: [/*#__PURE__*/_jsxDEV(PlanetGlow, {\n        color: baseColor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlanetImage, {\n        src: planetSrc,\n        alt: `${type} planet`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PlanetRing, {\n        color: baseColor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c0 = Planet;\nexport default Planet;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"PlanetContainer\");\n$RefreshReg$(_c2, \"PlanetImage\");\n$RefreshReg$(_c3, \"PlanetRing\");\n$RefreshReg$(_c4, \"PlanetGlow\");\n$RefreshReg$(_c5, \"OrbitPath\");\n$RefreshReg$(_c6, \"Orbit\");\n$RefreshReg$(_c7, \"FeaturePoint\");\n$RefreshReg$(_c8, \"ConnectionLine\");\n$RefreshReg$(_c9, \"OrbitFeaturePoint\");\n$RefreshReg$(_c0, \"Planet\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "css", "theme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "float", "orbitRotate", "secondaryOrbitRotate", "featurePointFloat", "shine", "orbitalShine", "textGlow", "getPulseAnimation", "color", "replace", "PlanetContainer", "div", "props", "size", "zIndex", "delay", "planetType", "toUpperCase", "active", "_c", "PlanetImage", "img", "_c2", "PlanetRing", "_c3", "PlanetGlow", "_c4", "OrbitPath", "_c5", "Orbit", "reverse", "duration", "_c6", "FeaturePoint", "colors", "primary", "index", "_c7", "ConnectionLine", "_c8", "getPointPosition", "total", "radius", "angle", "Math", "PI", "x", "cos", "y", "sin", "OrbitFeaturePoint", "icon", "totalPoints", "onClick", "position", "children", "style", "left", "top", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "_c9", "Planet", "type", "showOrbit", "orbitPoints", "activeFeatureIndex", "onFeatureClick", "planetSrc", "orbitColor", "baseColor", "length", "map", "src", "alt", "_c0", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Planets/Planet.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport styled, { keyframes, css } from 'styled-components';\nimport { theme } from '../../../styles/theme';\n\nconst float = keyframes`\n  0% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  50% {\n    transform: translateY(-15px) rotate(2deg);\n  }\n  100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n`;\n\nconst orbitRotate = keyframes`\n  from {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n  to {\n    transform: translate(-50%, -50%) rotate(360deg);\n  }\n`;\n\nconst secondaryOrbitRotate = keyframes`\n  from {\n    transform: translate(-50%, -50%) rotate(0deg);\n  }\n  to {\n    transform: translate(-50%, -50%) rotate(-360deg);\n  }\n`;\n\nconst featurePointFloat = keyframes`\n  0% {\n    transform: translateY(0px) scale(1);\n    box-shadow: 0 0 15px currentColor;\n  }\n  50% {\n    transform: translateY(-5px) scale(1.1);\n    box-shadow: 0 0 25px currentColor;\n  }\n  100% {\n    transform: translateY(0px) scale(1);\n    box-shadow: 0 0 15px currentColor;\n  }\n`;\n\nconst shine = keyframes`\n  0% {\n    opacity: 0.3;\n  }\n  50% {\n    opacity: 0.7;\n  }\n  100% {\n    opacity: 0.3;\n  }\n`;\n\nconst orbitalShine = keyframes`\n  0%, 100% {\n    opacity: 0.2;\n  }\n  50% {\n    opacity: 0.8;\n  }\n`;\n\nconst textGlow = keyframes`\n  0%, 100% {\n    text-shadow: 0 0 20px rgba(255, 255, 255, 0.4);\n  }\n  50% {\n    text-shadow: 0 0 30px rgba(255, 255, 255, 0.7), 0 0 40px currentColor;\n  }\n`;\n\ninterface PlanetImageProps {\n  active?: boolean;\n  color?: string;\n  size?: string;\n  delay?: string;\n  planetType: 'build' | 'scale';\n  zIndex?: number;\n}\n\n// Define pulse animation with proper typing\nconst getPulseAnimation = (color: string = 'rgba(92, 69, 255, 0.4)') => keyframes`\n  0% {\n    filter: drop-shadow(0 0 20px ${color});\n    transform: scale(1);\n  }\n  50% {\n    filter: drop-shadow(0 0 60px ${color.replace('0.4', '0.6')});\n    transform: scale(1.05);\n  }\n  100% {\n    filter: drop-shadow(0 0 20px ${color});\n    transform: scale(1);\n  }\n`;\n\nconst PlanetContainer = styled.div<PlanetImageProps>`\n  position: relative;\n  width: ${props => props.size || '300px'};\n  height: ${props => props.size || '300px'};\n  z-index: ${props => props.zIndex || 1};\n  animation: ${float} 8s ease-in-out infinite;\n  animation-delay: ${props => props.delay || '0s'};\n  transition: all 0.5s ease-in-out;\n  \n  &::after {\n    content: '${props => props.planetType.toUpperCase()}';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    color: white;\n    font-size: 2.5rem;\n    font-weight: bold;\n    text-shadow: 0 0 20px rgba(0, 0, 0, 0.7);\n    z-index: 3;\n    letter-spacing: 2px;\n    animation: ${textGlow} 3s infinite ease-in-out;\n    color: ${props => props.planetType === 'build' ? '#FFFFFF' : '#FFFFFF'};\n  }\n  \n  ${props => props.active && css`\n    animation: ${float} 8s ease-in-out infinite, ${getPulseAnimation(props.color)} 3s ease-in-out infinite;\n    transform: scale(1.05);\n  `}\n`;\n\nconst PlanetImage = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n  filter: drop-shadow(0 0 20px rgba(92, 69, 255, 0.5));\n  transition: all 0.3s ease-in-out;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0) 70%);\n    mix-blend-mode: overlay;\n    animation: ${shine} 5s infinite;\n  }\n`;\n\nconst PlanetRing = styled.div<{ color: string }>`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 110%;\n  height: 110%;\n  border-radius: 50%;\n  border: 2px solid ${props => props.color};\n  opacity: 0.3;\n  box-shadow: 0 0 15px ${props => props.color};\n  pointer-events: none;\n  z-index: 4;\n`;\n\nconst PlanetGlow = styled.div<{ color: string }>`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 130%;\n  height: 130%;\n  border-radius: 50%;\n  background: radial-gradient(\n    circle,\n    ${props => props.color}40 0%,\n    ${props => props.color}20 30%,\n    ${props => props.color}10 60%,\n    transparent 70%\n  );\n  filter: blur(20px);\n  opacity: 0.7;\n  z-index: 0;\n  pointer-events: none;\n`;\n\nconst OrbitPath = styled.div<{ size: string; color?: string }>`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: ${props => `calc(${props.size} * 1.5)`};\n  height: ${props => `calc(${props.size} * 1.5)`};\n  border: 1px solid ${props => props.color || 'rgba(92, 69, 255, 0.2)'};\n  border-radius: 50%;\n  z-index: 0;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: -15px;\n    left: -15px;\n    right: -15px;\n    bottom: -15px;\n    border: 1px solid ${props => props.color ? props.color.replace('0.2', '0.1') : 'rgba(92, 69, 255, 0.1)'};\n    border-radius: 50%;\n    animation: ${orbitalShine} 8s infinite ease-in-out;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -30px;\n    left: -30px;\n    right: -30px;\n    bottom: -30px;\n    border: 0.5px solid ${props => props.color ? props.color.replace('0.2', '0.05') : 'rgba(92, 69, 255, 0.05)'};\n    border-radius: 50%;\n    animation: ${orbitalShine} 12s infinite ease-in-out reverse;\n  }\n`;\n\ninterface OrbitProps {\n  size: string;\n  duration?: string;\n  color?: string;\n  reverse?: boolean;\n  delay?: string;\n}\n\nconst Orbit = styled.div<OrbitProps>`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: ${props => props.size};\n  height: ${props => props.size};\n  border: 1px solid ${props => props.color || 'rgba(92, 69, 255, 0.3)'};\n  border-radius: 50%;\n  z-index: 0;\n  transform: translate(-50%, -50%);\n  animation: ${props => props.reverse ? secondaryOrbitRotate : orbitRotate} ${props => props.duration || '30s'} linear infinite;\n  animation-delay: ${props => props.delay || '0s'};\n`;\n\nconst FeaturePoint = styled.div<{ color?: string; index: number; active?: boolean }>`\n  position: absolute;\n  width: 44px;\n  height: 44px;\n  border-radius: 50%;\n  background-color: ${props => props.color || theme.colors.primary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 0 15px ${props => props.color || theme.colors.primary};\n  animation: ${featurePointFloat} 4s ease-in-out infinite;\n  animation-delay: ${props => `${props.index * 0.5}s`};\n  z-index: 5;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  \n  ${props => props.active && css`\n    transform: scale(1.2);\n    box-shadow: 0 0 25px ${props.color || theme.colors.primary};\n  `}\n  \n  svg {\n    width: 20px;\n    height: 20px;\n    color: white;\n    filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.5));\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    left: -5px;\n    right: -5px;\n    bottom: -5px;\n    border-radius: 50%;\n    border: 1px solid ${props => props.color || theme.colors.primary};\n    opacity: 0.5;\n    animation: ${orbitalShine} 3s infinite;\n  }\n`;\n\nconst ConnectionLine = styled.div<{ color: string }>`\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  height: 2px;\n  background-color: ${props => props.color};\n  transform-origin: left center;\n  box-shadow: 0 0 8px ${props => props.color};\n  opacity: 0.7;\n  pointer-events: none;\n`;\n\n// Calculate position on orbit\nconst getPointPosition = (index: number, total: number, radius: number) => {\n  const angle = (index / total) * 2 * Math.PI;\n  const x = 50 + Math.cos(angle) * radius;\n  const y = 50 + Math.sin(angle) * radius;\n  return { x, y, angle };\n};\n\ninterface OrbitFeaturePointProps {\n  icon: React.ReactNode;\n  index: number;\n  totalPoints: number;\n  color?: string;\n  active?: boolean;\n  onClick?: () => void;\n}\n\nconst OrbitFeaturePoint: React.FC<OrbitFeaturePointProps> = ({ \n  icon, \n  index, \n  totalPoints, \n  color,\n  active,\n  onClick\n}) => {\n  const position = getPointPosition(index, totalPoints, 45);\n  \n  return (\n    <>\n      <FeaturePoint \n        style={{ \n          left: `${position.x}%`, \n          top: `${position.y}%`,\n          transform: 'translate(-50%, -50%)'\n        }}\n        color={color}\n        index={index}\n        active={active}\n        onClick={onClick}\n      >\n        {icon}\n      </FeaturePoint>\n      {active && (\n        <ConnectionLine \n          color={color || theme.colors.primary}\n          style={{ \n            left: '50%',\n            width: `${45}%`,\n            transform: `rotate(${position.angle}rad)`\n          }}\n        />\n      )}\n    </>\n  );\n};\n\ninterface PlanetProps {\n  type: 'build' | 'scale';\n  size?: string;\n  active?: boolean;\n  showOrbit?: boolean;\n  color?: string;\n  delay?: string;\n  zIndex?: number;\n  orbitPoints?: React.ReactNode[];\n  activeFeatureIndex?: number;\n  onFeatureClick?: (index: number) => void;\n}\n\nconst Planet: React.FC<PlanetProps> = ({ \n  type, \n  size = \"300px\", \n  active = false, \n  showOrbit = true,\n  color = 'rgba(92, 69, 255, 0.4)',\n  delay,\n  zIndex,\n  orbitPoints = [],\n  activeFeatureIndex,\n  onFeatureClick\n}) => {\n  const planetSrc = type === 'build' ? '/planet1.webp' : '/planet2.webp';\n  const orbitColor = type === 'build' ? 'rgba(0, 210, 194, 0.2)' : 'rgba(138, 112, 255, 0.2)';\n  const baseColor = type === 'build' ? '#00d2c2' : '#8a70ff';\n  \n  return (\n    <>\n      {showOrbit && <OrbitPath size={size} color={orbitColor} />}\n      \n      {orbitPoints.length > 0 && (\n        <>\n          <Orbit \n            size={`calc(${size} * 2.2)`} \n            duration=\"60s\"\n            color={orbitColor}\n          >\n            {orbitPoints.map((icon, index) => (\n              <OrbitFeaturePoint\n                key={index}\n                icon={icon}\n                index={index}\n                totalPoints={orbitPoints.length}\n                color={baseColor}\n                active={activeFeatureIndex === index}\n                onClick={() => onFeatureClick && onFeatureClick(index)}\n              />\n            ))}\n          </Orbit>\n          <Orbit \n            size={`calc(${size} * 1.8)`} \n            duration=\"40s\"\n            color={orbitColor}\n            reverse\n            delay=\"5s\"\n          />\n        </>\n      )}\n\n      <PlanetContainer \n        size={size} \n        active={active} \n        color={color}\n        delay={delay}\n        planetType={type}\n        zIndex={zIndex}\n      >\n        <PlanetGlow color={baseColor} />\n        <PlanetImage src={planetSrc} alt={`${type} planet`} />\n        <PlanetRing color={baseColor} />\n      </PlanetContainer>\n    </>\n  );\n};\n\nexport default Planet; "], "mappings": ";AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,OAAOC,MAAM,IAAIC,SAAS,EAAEC,GAAG,QAAQ,mBAAmB;AAC1D,SAASC,KAAK,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9C,MAAMC,KAAK,GAAGP,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMQ,WAAW,GAAGR,SAAS;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMS,oBAAoB,GAAGT,SAAS;AACtC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMU,iBAAiB,GAAGV,SAAS;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMW,KAAK,GAAGX,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMY,YAAY,GAAGZ,SAAS;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMa,QAAQ,GAAGb,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAWD;AACA,MAAMc,iBAAiB,GAAGA,CAACC,KAAa,GAAG,wBAAwB,KAAKf,SAAS;AACjF;AACA,mCAAmCe,KAAK;AACxC;AACA;AACA;AACA,mCAAmCA,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;AAC9D;AACA;AACA;AACA,mCAAmCD,KAAK;AACxC;AACA;AACA,CAAC;AAED,MAAME,eAAe,GAAGlB,MAAM,CAACmB,GAAqB;AACpD;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,IAAI,IAAI,OAAO;AACzC,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI,IAAI,OAAO;AAC1C,aAAaD,KAAK,IAAIA,KAAK,CAACE,MAAM,IAAI,CAAC;AACvC,eAAed,KAAK;AACpB,qBAAqBY,KAAK,IAAIA,KAAK,CAACG,KAAK,IAAI,IAAI;AACjD;AACA;AACA;AACA,gBAAgBH,KAAK,IAAIA,KAAK,CAACI,UAAU,CAACC,WAAW,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBX,QAAQ;AACzB,aAAaM,KAAK,IAAIA,KAAK,CAACI,UAAU,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;AAC1E;AACA;AACA,IAAIJ,KAAK,IAAIA,KAAK,CAACM,MAAM,IAAIxB,GAAG;AAChC,iBAAiBM,KAAK,6BAA6BO,iBAAiB,CAACK,KAAK,CAACJ,KAAK,CAAC;AACjF;AACA,GAAG;AACH,CAAC;AAACW,EAAA,GA7BIT,eAAe;AA+BrB,MAAMU,WAAW,GAAG5B,MAAM,CAAC6B,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiBjB,KAAK;AACtB;AACA,CAAC;AAACkB,GAAA,GAlBIF,WAAW;AAoBjB,MAAMG,UAAU,GAAG/B,MAAM,CAACmB,GAAsB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC1C;AACA,yBAAyBI,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC7C;AACA;AACA,CAAC;AAACgB,GAAA,GAbID,UAAU;AAehB,MAAME,UAAU,GAAGjC,MAAM,CAACmB,GAAsB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC1B,MAAMI,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC1B,MAAMI,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkB,GAAA,GAnBID,UAAU;AAqBhB,MAAME,SAAS,GAAGnC,MAAM,CAACmB,GAAqC;AAC9D;AACA;AACA;AACA;AACA,WAAWC,KAAK,IAAI,QAAQA,KAAK,CAACC,IAAI,SAAS;AAC/C,YAAYD,KAAK,IAAI,QAAQA,KAAK,CAACC,IAAI,SAAS;AAChD,sBAAsBD,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAI,wBAAwB;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBI,KAAK,IAAIA,KAAK,CAACJ,KAAK,GAAGI,KAAK,CAACJ,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,wBAAwB;AAC3G;AACA,iBAAiBJ,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0BO,KAAK,IAAIA,KAAK,CAACJ,KAAK,GAAGI,KAAK,CAACJ,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,yBAAyB;AAC/G;AACA,iBAAiBJ,YAAY;AAC7B;AACA,CAAC;AAACuB,GAAA,GAlCID,SAAS;AA4Cf,MAAME,KAAK,GAAGrC,MAAM,CAACmB,GAAe;AACpC;AACA;AACA;AACA,WAAWC,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC9B,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC/B,sBAAsBD,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAI,wBAAwB;AACtE;AACA;AACA;AACA,eAAeI,KAAK,IAAIA,KAAK,CAACkB,OAAO,GAAG5B,oBAAoB,GAAGD,WAAW,IAAIW,KAAK,IAAIA,KAAK,CAACmB,QAAQ,IAAI,KAAK;AAC9G,qBAAqBnB,KAAK,IAAIA,KAAK,CAACG,KAAK,IAAI,IAAI;AACjD,CAAC;AAACiB,GAAA,GAZIH,KAAK;AAcX,MAAMI,YAAY,GAAGzC,MAAM,CAACmB,GAAwD;AACpF;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAIb,KAAK,CAACuC,MAAM,CAACC,OAAO;AAClE;AACA;AACA;AACA,yBAAyBvB,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAIb,KAAK,CAACuC,MAAM,CAACC,OAAO;AACrE,eAAehC,iBAAiB;AAChC,qBAAqBS,KAAK,IAAI,GAAGA,KAAK,CAACwB,KAAK,GAAG,GAAG,GAAG;AACrD;AACA;AACA;AACA;AACA,IAAIxB,KAAK,IAAIA,KAAK,CAACM,MAAM,IAAIxB,GAAG;AAChC;AACA,2BAA2BkB,KAAK,CAACJ,KAAK,IAAIb,KAAK,CAACuC,MAAM,CAACC,OAAO;AAC9D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBvB,KAAK,IAAIA,KAAK,CAACJ,KAAK,IAAIb,KAAK,CAACuC,MAAM,CAACC,OAAO;AACpE;AACA,iBAAiB9B,YAAY;AAC7B;AACA,CAAC;AAACgC,GAAA,GAxCIJ,YAAY;AA0ClB,MAAMK,cAAc,GAAG9C,MAAM,CAACmB,GAAsB;AACpD;AACA;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC1C;AACA,wBAAwBI,KAAK,IAAIA,KAAK,CAACJ,KAAK;AAC5C;AACA;AACA,CAAC;;AAED;AAAA+B,GAAA,GAZMD,cAAc;AAapB,MAAME,gBAAgB,GAAGA,CAACJ,KAAa,EAAEK,KAAa,EAAEC,MAAc,KAAK;EACzE,MAAMC,KAAK,GAAIP,KAAK,GAAGK,KAAK,GAAI,CAAC,GAAGG,IAAI,CAACC,EAAE;EAC3C,MAAMC,CAAC,GAAG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC,GAAGD,MAAM;EACvC,MAAMM,CAAC,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACN,KAAK,CAAC,GAAGD,MAAM;EACvC,OAAO;IAAEI,CAAC;IAAEE,CAAC;IAAEL;EAAM,CAAC;AACxB,CAAC;AAWD,MAAMO,iBAAmD,GAAGA,CAAC;EAC3DC,IAAI;EACJf,KAAK;EACLgB,WAAW;EACX5C,KAAK;EACLU,MAAM;EACNmC;AACF,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAGd,gBAAgB,CAACJ,KAAK,EAAEgB,WAAW,EAAE,EAAE,CAAC;EAEzD,oBACEvD,OAAA,CAAAE,SAAA;IAAAwD,QAAA,gBACE1D,OAAA,CAACoC,YAAY;MACXuB,KAAK,EAAE;QACLC,IAAI,EAAE,GAAGH,QAAQ,CAACR,CAAC,GAAG;QACtBY,GAAG,EAAE,GAAGJ,QAAQ,CAACN,CAAC,GAAG;QACrBW,SAAS,EAAE;MACb,CAAE;MACFnD,KAAK,EAAEA,KAAM;MACb4B,KAAK,EAAEA,KAAM;MACblB,MAAM,EAAEA,MAAO;MACfmC,OAAO,EAAEA,OAAQ;MAAAE,QAAA,EAEhBJ;IAAI;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,EACd7C,MAAM,iBACLrB,OAAA,CAACyC,cAAc;MACb9B,KAAK,EAAEA,KAAK,IAAIb,KAAK,CAACuC,MAAM,CAACC,OAAQ;MACrCqB,KAAK,EAAE;QACLC,IAAI,EAAE,KAAK;QACXO,KAAK,EAAE,GAAG,EAAE,GAAG;QACfL,SAAS,EAAE,UAAUL,QAAQ,CAACX,KAAK;MACrC;IAAE;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACF;EAAA,eACD,CAAC;AAEP,CAAC;AAACE,GAAA,GArCIf,iBAAmD;AAoDzD,MAAMgB,MAA6B,GAAGA,CAAC;EACrCC,IAAI;EACJtD,IAAI,GAAG,OAAO;EACdK,MAAM,GAAG,KAAK;EACdkD,SAAS,GAAG,IAAI;EAChB5D,KAAK,GAAG,wBAAwB;EAChCO,KAAK;EACLD,MAAM;EACNuD,WAAW,GAAG,EAAE;EAChBC,kBAAkB;EAClBC;AACF,CAAC,KAAK;EACJ,MAAMC,SAAS,GAAGL,IAAI,KAAK,OAAO,GAAG,eAAe,GAAG,eAAe;EACtE,MAAMM,UAAU,GAAGN,IAAI,KAAK,OAAO,GAAG,wBAAwB,GAAG,0BAA0B;EAC3F,MAAMO,SAAS,GAAGP,IAAI,KAAK,OAAO,GAAG,SAAS,GAAG,SAAS;EAE1D,oBACEtE,OAAA,CAAAE,SAAA;IAAAwD,QAAA,GACGa,SAAS,iBAAIvE,OAAA,CAAC8B,SAAS;MAACd,IAAI,EAAEA,IAAK;MAACL,KAAK,EAAEiE;IAAW;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEzDM,WAAW,CAACM,MAAM,GAAG,CAAC,iBACrB9E,OAAA,CAAAE,SAAA;MAAAwD,QAAA,gBACE1D,OAAA,CAACgC,KAAK;QACJhB,IAAI,EAAE,QAAQA,IAAI,SAAU;QAC5BkB,QAAQ,EAAC,KAAK;QACdvB,KAAK,EAAEiE,UAAW;QAAAlB,QAAA,EAEjBc,WAAW,CAACO,GAAG,CAAC,CAACzB,IAAI,EAAEf,KAAK,kBAC3BvC,OAAA,CAACqD,iBAAiB;UAEhBC,IAAI,EAAEA,IAAK;UACXf,KAAK,EAAEA,KAAM;UACbgB,WAAW,EAAEiB,WAAW,CAACM,MAAO;UAChCnE,KAAK,EAAEkE,SAAU;UACjBxD,MAAM,EAAEoD,kBAAkB,KAAKlC,KAAM;UACrCiB,OAAO,EAAEA,CAAA,KAAMkB,cAAc,IAAIA,cAAc,CAACnC,KAAK;QAAE,GANlDA,KAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACRlE,OAAA,CAACgC,KAAK;QACJhB,IAAI,EAAE,QAAQA,IAAI,SAAU;QAC5BkB,QAAQ,EAAC,KAAK;QACdvB,KAAK,EAAEiE,UAAW;QAClB3C,OAAO;QACPf,KAAK,EAAC;MAAI;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC;IAAA,eACF,CACH,eAEDlE,OAAA,CAACa,eAAe;MACdG,IAAI,EAAEA,IAAK;MACXK,MAAM,EAAEA,MAAO;MACfV,KAAK,EAAEA,KAAM;MACbO,KAAK,EAAEA,KAAM;MACbC,UAAU,EAAEmD,IAAK;MACjBrD,MAAM,EAAEA,MAAO;MAAAyC,QAAA,gBAEf1D,OAAA,CAAC4B,UAAU;QAACjB,KAAK,EAAEkE;MAAU;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChClE,OAAA,CAACuB,WAAW;QAACyD,GAAG,EAAEL,SAAU;QAACM,GAAG,EAAE,GAAGX,IAAI;MAAU;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDlE,OAAA,CAAC0B,UAAU;QAACf,KAAK,EAAEkE;MAAU;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAAA,eAClB,CAAC;AAEP,CAAC;AAACgB,GAAA,GA/DIb,MAA6B;AAiEnC,eAAeA,MAAM;AAAC,IAAA/C,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAA0B,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAA7D,EAAA;AAAA6D,YAAA,CAAA1D,GAAA;AAAA0D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAApD,GAAA;AAAAoD,YAAA,CAAAhD,GAAA;AAAAgD,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}