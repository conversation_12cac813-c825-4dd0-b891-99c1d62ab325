{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Features/BackgroundImage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BackgroundContainer = styled.div`\n  position: ${props => props.fixed ? 'fixed' : 'absolute'};\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n  opacity: ${props => props.loaded ? 1 : 0};\n  transition: opacity 0.5s ease;\n`;\n_c = BackgroundContainer;\nconst Image = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  object-position: center;\n`;\n_c2 = Image;\nconst Overlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(14, 14, 28, 0.8);\n  z-index: 1;\n`;\n\n// Add a subtle noise texture overlay\n_c3 = Overlay;\nconst NoiseOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: url('data:image/png;base64,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');\n  opacity: 0.02;\n  z-index: 2;\n  pointer-events: none;\n`;\n_c4 = NoiseOverlay;\nconst BackgroundImage = ({\n  src,\n  className,\n  overlayOpacity = 0.8,\n  fixed = true\n}) => {\n  _s();\n  const [loaded, setLoaded] = useState(false);\n  useEffect(() => {\n    const img = document.createElement('img');\n    img.src = src;\n    img.onload = () => setLoaded(true);\n  }, [src]);\n  return /*#__PURE__*/_jsxDEV(BackgroundContainer, {\n    className: className,\n    loaded: loaded,\n    fixed: fixed,\n    children: [/*#__PURE__*/_jsxDEV(Image, {\n      src: src,\n      alt: \"Background\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Overlay, {\n      style: {\n        backgroundColor: `rgba(14, 14, 28, ${overlayOpacity})`\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(NoiseOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(BackgroundImage, \"I8RUn14npbk54TheiKdNt4wCpsU=\");\n_c5 = BackgroundImage;\nexport default BackgroundImage;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"BackgroundContainer\");\n$RefreshReg$(_c2, \"Image\");\n$RefreshReg$(_c3, \"Overlay\");\n$RefreshReg$(_c4, \"NoiseOverlay\");\n$RefreshReg$(_c5, \"BackgroundImage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "styled", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "div", "props", "fixed", "loaded", "_c", "Image", "img", "_c2", "Overlay", "_c3", "NoiseOverlay", "_c4", "BackgroundImage", "src", "className", "overlayOpacity", "_s", "setLoaded", "document", "createElement", "onload", "children", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundColor", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Features/BackgroundImage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\n\nconst BackgroundContainer = styled.div<{ loaded: boolean; fixed?: boolean }>`\n  position: ${props => props.fixed ? 'fixed' : 'absolute'};\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 0;\n  opacity: ${props => props.loaded ? 1 : 0};\n  transition: opacity 0.5s ease;\n`;\n\nconst Image = styled.img`\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  object-position: center;\n`;\n\nconst Overlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(14, 14, 28, 0.8);\n  z-index: 1;\n`;\n\n// Add a subtle noise texture overlay\nconst NoiseOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: url('data:image/png;base64,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');\n  opacity: 0.02;\n  z-index: 2;\n  pointer-events: none;\n`;\n\ninterface BackgroundImageProps {\n  src: string;\n  className?: string;\n  overlayOpacity?: number;\n  fixed?: boolean;\n}\n\nconst BackgroundImage: React.FC<BackgroundImageProps> = ({ \n  src, \n  className,\n  overlayOpacity = 0.8,\n  fixed = true\n}) => {\n  const [loaded, setLoaded] = useState(false);\n\n  useEffect(() => {\n    const img = document.createElement('img');\n    img.src = src;\n    img.onload = () => setLoaded(true);\n  }, [src]);\n\n  return (\n    <BackgroundContainer className={className} loaded={loaded} fixed={fixed}>\n      <Image src={src} alt=\"Background\" />\n      <Overlay style={{ backgroundColor: `rgba(14, 14, 28, ${overlayOpacity})` }} />\n      <NoiseOverlay />\n    </BackgroundContainer>\n  );\n};\n\nexport default BackgroundImage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,mBAAmB,GAAGH,MAAM,CAACI,GAAyC;AAC5E,cAAcC,KAAK,IAAIA,KAAK,CAACC,KAAK,GAAG,OAAO,GAAG,UAAU;AACzD;AACA;AACA;AACA;AACA;AACA,aAAaD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC;AAC1C;AACA,CAAC;AAACC,EAAA,GATIL,mBAAmB;AAWzB,MAAMM,KAAK,GAAGT,MAAM,CAACU,GAAG;AACxB;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,KAAK;AAOX,MAAMG,OAAO,GAAGZ,MAAM,CAACI,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAS,GAAA,GAVMD,OAAO;AAWb,MAAME,YAAY,GAAGd,MAAM,CAACI,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAVID,YAAY;AAmBlB,MAAME,eAA+C,GAAGA,CAAC;EACvDC,GAAG;EACHC,SAAS;EACTC,cAAc,GAAG,GAAG;EACpBb,KAAK,GAAG;AACV,CAAC,KAAK;EAAAc,EAAA;EACJ,MAAM,CAACb,MAAM,EAAEc,SAAS,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAE3CD,SAAS,CAAC,MAAM;IACd,MAAMY,GAAG,GAAGY,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACzCb,GAAG,CAACO,GAAG,GAAGA,GAAG;IACbP,GAAG,CAACc,MAAM,GAAG,MAAMH,SAAS,CAAC,IAAI,CAAC;EACpC,CAAC,EAAE,CAACJ,GAAG,CAAC,CAAC;EAET,oBACEf,OAAA,CAACC,mBAAmB;IAACe,SAAS,EAAEA,SAAU;IAACX,MAAM,EAAEA,MAAO;IAACD,KAAK,EAAEA,KAAM;IAAAmB,QAAA,gBACtEvB,OAAA,CAACO,KAAK;MAACQ,GAAG,EAAEA,GAAI;MAACS,GAAG,EAAC;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpC5B,OAAA,CAACU,OAAO;MAACmB,KAAK,EAAE;QAAEC,eAAe,EAAE,oBAAoBb,cAAc;MAAI;IAAE;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC9E5B,OAAA,CAACY,YAAY;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE1B,CAAC;AAACV,EAAA,CArBIJ,eAA+C;AAAAiB,GAAA,GAA/CjB,eAA+C;AAuBrD,eAAeA,eAAe;AAAC,IAAAR,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAkB,GAAA;AAAAC,YAAA,CAAA1B,EAAA;AAAA0B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}