{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Cards/FeatureCard.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Card = styled.div`\n  position: relative;\n  padding: 2rem;\n  background-color: rgba(20, 20, 40, 0.7);\n  border-radius: ${theme.borderRadius.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.active ? props.borderColor || theme.colors.primary : 'rgba(92, 69, 255, 0.1)'};\n  \n  &:hover {\n    border-color: ${props => props.borderColor || theme.colors.primary};\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n  }\n  \n  ${props => props.active && `\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);\n  `}\n`;\n_c = Card;\nconst CardTitle = styled.h3`\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.75rem;\n  color: ${props => props.color || theme.colors.text};\n`;\n_c2 = CardTitle;\nconst CardContent = styled.p`\n  font-size: 0.9rem;\n  color: ${theme.colors.textSecondary};\n  margin-bottom: 1.5rem;\n  line-height: 1.5;\n`;\n_c3 = CardContent;\nconst CardButton = styled.a`\n  display: inline-block;\n  padding: 0.5rem 1.2rem;\n  background-color: ${props => props.color || theme.colors.primary};\n  color: white;\n  border-radius: ${theme.borderRadius.pill};\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-decoration: none;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background-color: ${props => {\n  const color = props.color || theme.colors.primary;\n  return color === theme.colors.primary ? theme.colors.primaryHover : color;\n}};\n    transform: translateY(-2px);\n  }\n`;\n_c4 = CardButton;\nconst IconContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background-color: ${props => props.color || theme.colors.primary}33;\n  margin-bottom: 1.25rem;\n  \n  svg {\n    width: 24px;\n    height: 24px;\n    color: ${props => props.color || theme.colors.primary};\n  }\n`;\n_c5 = IconContainer;\nconst FeatureCard = ({\n  title,\n  description,\n  buttonText = \"Read more\",\n  buttonLink = \"#\",\n  icon,\n  color = theme.colors.primary,\n  active = false,\n  onClick,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    active: active,\n    color: color,\n    borderColor: color,\n    onClick: onClick,\n    className: className,\n    children: [icon && /*#__PURE__*/_jsxDEV(IconContainer, {\n      color: color,\n      children: icon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 16\n    }, this), /*#__PURE__*/_jsxDEV(CardTitle, {\n      color: color,\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n      children: description\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardButton, {\n      href: buttonLink,\n      color: color,\n      children: buttonText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 5\n  }, this);\n};\n_c6 = FeatureCard;\nexport default FeatureCard;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c2, \"CardTitle\");\n$RefreshReg$(_c3, \"CardContent\");\n$RefreshReg$(_c4, \"CardButton\");\n$RefreshReg$(_c5, \"IconContainer\");\n$RefreshReg$(_c6, \"FeatureCard\");", "map": {"version": 3, "names": ["React", "styled", "theme", "jsxDEV", "_jsxDEV", "Card", "div", "borderRadius", "medium", "props", "active", "borderColor", "colors", "primary", "_c", "CardTitle", "h3", "color", "text", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "p", "textSecondary", "_c3", "CardButton", "a", "pill", "primaryHover", "_c4", "IconContainer", "_c5", "FeatureCard", "title", "description", "buttonText", "buttonLink", "icon", "onClick", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c6", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Cards/FeatureCard.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../../styles/theme';\n\ninterface CardProps {\n  active?: boolean;\n  color?: string;\n  borderColor?: string;\n}\n\nconst Card = styled.div<CardProps>`\n  position: relative;\n  padding: 2rem;\n  background-color: rgba(20, 20, 40, 0.7);\n  border-radius: ${theme.borderRadius.medium};\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.active \n    ? props.borderColor || theme.colors.primary \n    : 'rgba(92, 69, 255, 0.1)'};\n  \n  &:hover {\n    border-color: ${props => props.borderColor || theme.colors.primary};\n    transform: translateY(-5px);\n    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);\n  }\n  \n  ${props => props.active && `\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25);\n  `}\n`;\n\nconst CardTitle = styled.h3<{ color?: string }>`\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 0.75rem;\n  color: ${props => props.color || theme.colors.text};\n`;\n\nconst CardContent = styled.p`\n  font-size: 0.9rem;\n  color: ${theme.colors.textSecondary};\n  margin-bottom: 1.5rem;\n  line-height: 1.5;\n`;\n\nconst CardButton = styled.a<{ color?: string }>`\n  display: inline-block;\n  padding: 0.5rem 1.2rem;\n  background-color: ${props => props.color || theme.colors.primary};\n  color: white;\n  border-radius: ${theme.borderRadius.pill};\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-decoration: none;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background-color: ${props => {\n      const color = props.color || theme.colors.primary;\n      return color === theme.colors.primary ? theme.colors.primaryHover : color;\n    }};\n    transform: translateY(-2px);\n  }\n`;\n\nconst IconContainer = styled.div<{ color?: string }>`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  background-color: ${props => props.color || theme.colors.primary}33;\n  margin-bottom: 1.25rem;\n  \n  svg {\n    width: 24px;\n    height: 24px;\n    color: ${props => props.color || theme.colors.primary};\n  }\n`;\n\ninterface FeatureCardProps {\n  title: string;\n  description: string;\n  buttonText?: string;\n  buttonLink?: string;\n  icon?: React.ReactNode;\n  color?: string;\n  active?: boolean;\n  onClick?: () => void;\n  className?: string;\n}\n\nconst FeatureCard: React.FC<FeatureCardProps> = ({\n  title,\n  description,\n  buttonText = \"Read more\",\n  buttonLink = \"#\",\n  icon,\n  color = theme.colors.primary,\n  active = false,\n  onClick,\n  className\n}) => {\n  return (\n    <Card \n      active={active} \n      color={color} \n      borderColor={color}\n      onClick={onClick}\n      className={className}\n    >\n      {icon && <IconContainer color={color}>{icon}</IconContainer>}\n      <CardTitle color={color}>{title}</CardTitle>\n      <CardContent>{description}</CardContent>\n      <CardButton href={buttonLink} color={color}>{buttonText}</CardButton>\n    </Card>\n  );\n};\n\nexport default FeatureCard; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQ9C,MAAMC,IAAI,GAAGJ,MAAM,CAACK,GAAc;AAClC;AACA;AACA;AACA,mBAAmBJ,KAAK,CAACK,YAAY,CAACC,MAAM;AAC5C;AACA;AACA;AACA,sBAAsBC,KAAK,IAAIA,KAAK,CAACC,MAAM,GACrCD,KAAK,CAACE,WAAW,IAAIT,KAAK,CAACU,MAAM,CAACC,OAAO,GACzC,wBAAwB;AAC9B;AACA;AACA,oBAAoBJ,KAAK,IAAIA,KAAK,CAACE,WAAW,IAAIT,KAAK,CAACU,MAAM,CAACC,OAAO;AACtE;AACA;AACA;AACA;AACA,IAAIJ,KAAK,IAAIA,KAAK,CAACC,MAAM,IAAI;AAC7B;AACA;AACA,GAAG;AACH,CAAC;AAACI,EAAA,GAtBIT,IAAI;AAwBV,MAAMU,SAAS,GAAGd,MAAM,CAACe,EAAsB;AAC/C;AACA;AACA;AACA,WAAWP,KAAK,IAAIA,KAAK,CAACQ,KAAK,IAAIf,KAAK,CAACU,MAAM,CAACM,IAAI;AACpD,CAAC;AAACC,GAAA,GALIJ,SAAS;AAOf,MAAMK,WAAW,GAAGnB,MAAM,CAACoB,CAAC;AAC5B;AACA,WAAWnB,KAAK,CAACU,MAAM,CAACU,aAAa;AACrC;AACA;AACA,CAAC;AAACC,GAAA,GALIH,WAAW;AAOjB,MAAMI,UAAU,GAAGvB,MAAM,CAACwB,CAAqB;AAC/C;AACA;AACA,sBAAsBhB,KAAK,IAAIA,KAAK,CAACQ,KAAK,IAAIf,KAAK,CAACU,MAAM,CAACC,OAAO;AAClE;AACA,mBAAmBX,KAAK,CAACK,YAAY,CAACmB,IAAI;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBjB,KAAK,IAAI;EAC3B,MAAMQ,KAAK,GAAGR,KAAK,CAACQ,KAAK,IAAIf,KAAK,CAACU,MAAM,CAACC,OAAO;EACjD,OAAOI,KAAK,KAAKf,KAAK,CAACU,MAAM,CAACC,OAAO,GAAGX,KAAK,CAACU,MAAM,CAACe,YAAY,GAAGV,KAAK;AAC3E,CAAC;AACL;AACA;AACA,CAAC;AAACW,GAAA,GAlBIJ,UAAU;AAoBhB,MAAMK,aAAa,GAAG5B,MAAM,CAACK,GAAuB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsBG,KAAK,IAAIA,KAAK,CAACQ,KAAK,IAAIf,KAAK,CAACU,MAAM,CAACC,OAAO;AAClE;AACA;AACA;AACA;AACA;AACA,aAAaJ,KAAK,IAAIA,KAAK,CAACQ,KAAK,IAAIf,KAAK,CAACU,MAAM,CAACC,OAAO;AACzD;AACA,CAAC;AAACiB,GAAA,GAfID,aAAa;AA6BnB,MAAME,WAAuC,GAAGA,CAAC;EAC/CC,KAAK;EACLC,WAAW;EACXC,UAAU,GAAG,WAAW;EACxBC,UAAU,GAAG,GAAG;EAChBC,IAAI;EACJnB,KAAK,GAAGf,KAAK,CAACU,MAAM,CAACC,OAAO;EAC5BH,MAAM,GAAG,KAAK;EACd2B,OAAO;EACPC;AACF,CAAC,KAAK;EACJ,oBACElC,OAAA,CAACC,IAAI;IACHK,MAAM,EAAEA,MAAO;IACfO,KAAK,EAAEA,KAAM;IACbN,WAAW,EAAEM,KAAM;IACnBoB,OAAO,EAAEA,OAAQ;IACjBC,SAAS,EAAEA,SAAU;IAAAC,QAAA,GAEpBH,IAAI,iBAAIhC,OAAA,CAACyB,aAAa;MAACZ,KAAK,EAAEA,KAAM;MAAAsB,QAAA,EAAEH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAgB,CAAC,eAC5DvC,OAAA,CAACW,SAAS;MAACE,KAAK,EAAEA,KAAM;MAAAsB,QAAA,EAAEP;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC5CvC,OAAA,CAACgB,WAAW;MAAAmB,QAAA,EAAEN;IAAW;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAc,CAAC,eACxCvC,OAAA,CAACoB,UAAU;MAACoB,IAAI,EAAET,UAAW;MAAClB,KAAK,EAAEA,KAAM;MAAAsB,QAAA,EAAEL;IAAU;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEX,CAAC;AAACE,GAAA,GAzBId,WAAuC;AA2B7C,eAAeA,WAAW;AAAC,IAAAjB,EAAA,EAAAK,GAAA,EAAAI,GAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAe,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}