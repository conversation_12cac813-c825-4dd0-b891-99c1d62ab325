{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Icons.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TestnetIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 16V12\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 8H12.01\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 4,\n  columnNumber: 3\n}, this);\n_c = TestnetIcon;\nexport const IntegrationIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 3H21V8\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M4 20L21 3\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M21 16V21H16\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M15 15L21 21\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M4 4L9 9\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 12,\n  columnNumber: 3\n}, this);\n_c2 = IntegrationIcon;\nexport const DevelopmentIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M8 9L12 5L16 9\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 15L12 19L8 15\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 5V19\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 22,\n  columnNumber: 3\n}, this);\n_c3 = DevelopmentIcon;\nexport const RPCIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 17.58C20.9 16.67 20.9 15.19 20 14.29C19.1 13.39 17.6 13.39 16.7 14.29\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M14 17.58C14.9 16.67 14.9 15.19 14 14.29C13.1 13.39 11.6 13.39 10.7 14.29C9.8 15.19 9.8 16.67 10.7 17.58C11.6 18.48 13.1 18.48 14 17.58Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M17 22V21\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M7 22V21\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 22V19\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M8 17.58C7.1 16.67 7.1 15.19 8 14.29C8.9 13.39 10.4 13.39 11.3 14.29\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 12C20 8.13 16.87 5 13 5H11C7.13 5 4 8.13 4 12C4 15.87 7.13 19 11 19H13C16.87 19 20 15.87 20 12Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 30,\n  columnNumber: 3\n}, this);\n_c4 = RPCIcon;\nexport const CollaborationIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 42,\n  columnNumber: 3\n}, this);\n_c5 = CollaborationIcon;\nexport const MonitoringIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M10 3H3V10H10V3Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M21 3H14V10H21V3Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M21 14H14V21H21V14Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M10 14H3V21H10V14Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 51,\n  columnNumber: 3\n}, this);\n_c6 = MonitoringIcon;\nexport const FrameworkIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 60,\n  columnNumber: 3\n}, this);\n_c7 = FrameworkIcon;\nexport const GlobalIcon = ({\n  color = '#5c45ff'\n}) => /*#__PURE__*/_jsxDEV(\"svg\", {\n  width: \"24\",\n  height: \"24\",\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  xmlns: \"http://www.w3.org/2000/svg\",\n  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M2 12H22\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n    d: \"M12 2C14.5013 4.73835 15.9228 8.29203 16 12C15.9228 15.708 14.5013 19.2616 12 22C9.49872 19.2616 8.07725 15.708 8 12C8.07725 8.29203 9.49872 4.73835 12 2V2Z\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 67,\n  columnNumber: 3\n}, this);\n_c8 = GlobalIcon;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"TestnetIcon\");\n$RefreshReg$(_c2, \"IntegrationIcon\");\n$RefreshReg$(_c3, \"DevelopmentIcon\");\n$RefreshReg$(_c4, \"RPCIcon\");\n$RefreshReg$(_c5, \"CollaborationIcon\");\n$RefreshReg$(_c6, \"MonitoringIcon\");\n$RefreshReg$(_c7, \"FrameworkIcon\");\n$RefreshReg$(_c8, \"GlobalIcon\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TestnetIcon", "color", "width", "height", "viewBox", "fill", "xmlns", "children", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "IntegrationIcon", "_c2", "DevelopmentIcon", "_c3", "RPCIcon", "_c4", "CollaborationIcon", "_c5", "MonitoringIcon", "_c6", "FrameworkIcon", "_c7", "GlobalIcon", "_c8", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Icons.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const TestnetIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M12 16V12\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M12 8H12.01\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const IntegrationIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M16 3H21V8\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M4 20L21 3\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M21 16V21H16\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M15 15L21 21\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M4 4L9 9\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const DevelopmentIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M8 9L12 5L16 9\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M16 15L12 19L8 15\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M12 5V19\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const RPCIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M20 17.58C20.9 16.67 20.9 15.19 20 14.29C19.1 13.39 17.6 13.39 16.7 14.29\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M14 17.58C14.9 16.67 14.9 15.19 14 14.29C13.1 13.39 11.6 13.39 10.7 14.29C9.8 15.19 9.8 16.67 10.7 17.58C11.6 18.48 13.1 18.48 14 17.58Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M17 22V21\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M7 22V21\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M12 22V19\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M8 17.58C7.1 16.67 7.1 15.19 8 14.29C8.9 13.39 10.4 13.39 11.3 14.29\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M20 12C20 8.13 16.87 5 13 5H11C7.13 5 4 8.13 4 12C4 15.87 7.13 19 11 19H13C16.87 19 20 15.87 20 12Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const CollaborationIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M23 21V19C22.9993 18.1137 22.7044 17.2528 22.1614 16.5523C21.6184 15.8519 20.8581 15.3516 20 15.13\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M16 3.13C16.8604 3.35031 17.623 3.85071 18.1676 4.55232C18.7122 5.25392 19.0078 6.11683 19.0078 7.005C19.0078 7.89318 18.7122 8.75608 18.1676 9.45769C17.623 10.1593 16.8604 10.6597 16 10.88\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const MonitoringIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M10 3H3V10H10V3Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M21 3H14V10H21V3Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M21 14H14V21H21V14Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M10 14H3V21H10V14Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const FrameworkIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M20 7H4C2.89543 7 2 7.89543 2 9V19C2 20.1046 2.89543 21 4 21H20C21.1046 21 22 20.1046 22 19V9C22 7.89543 21.1046 7 20 7Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M16 21V5C16 4.46957 15.7893 3.96086 15.4142 3.58579C15.0391 3.21071 14.5304 3 14 3H10C9.46957 3 8.96086 3.21071 8.58579 3.58579C8.21071 3.96086 8 4.46957 8 5V21\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n);\n\nexport const GlobalIcon = ({ color = '#5c45ff' }) => (\n  <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M2 12H22\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n    <path d=\"M12 2C14.5013 4.73835 15.9228 8.29203 16 12C15.9228 15.708 14.5013 19.2616 12 22C9.49872 19.2616 8.07725 15.708 8 12C8.07725 8.29203 9.49872 4.73835 12 2V2Z\" stroke={color} strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n  </svg>\n); "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,WAAW,GAAGA,CAAC;EAAEC,KAAK,GAAG;AAAU,CAAC,kBAC/CF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,mHAAmH;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACzMjB,OAAA;IAAMS,CAAC,EAAC,WAAW;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACjGjB,OAAA;IAAMS,CAAC,EAAC,aAAa;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAChG,CACN;AAACC,EAAA,GANWjB,WAAW;AAQxB,OAAO,MAAMkB,eAAe,GAAGA,CAAC;EAAEjB,KAAK,GAAG;AAAU,CAAC,kBACnDF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,YAAY;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAClGjB,OAAA;IAAMS,CAAC,EAAC,YAAY;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAClGjB,OAAA;IAAMS,CAAC,EAAC,cAAc;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACpGjB,OAAA;IAAMS,CAAC,EAAC,cAAc;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACpGjB,OAAA;IAAMS,CAAC,EAAC,UAAU;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7F,CACN;AAACG,GAAA,GARWD,eAAe;AAU5B,OAAO,MAAME,eAAe,GAAGA,CAAC;EAAEnB,KAAK,GAAG;AAAU,CAAC,kBACnDF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,gBAAgB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACtGjB,OAAA;IAAMS,CAAC,EAAC,mBAAmB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACzGjB,OAAA;IAAMS,CAAC,EAAC,UAAU;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAC7F,CACN;AAACK,GAAA,GANWD,eAAe;AAQ5B,OAAO,MAAME,OAAO,GAAGA,CAAC;EAAErB,KAAK,GAAG;AAAU,CAAC,kBAC3CF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,2EAA2E;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACjKjB,OAAA;IAAMS,CAAC,EAAC,0IAA0I;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAChOjB,OAAA;IAAMS,CAAC,EAAC,WAAW;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACjGjB,OAAA;IAAMS,CAAC,EAAC,UAAU;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAChGjB,OAAA;IAAMS,CAAC,EAAC,WAAW;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACjGjB,OAAA;IAAMS,CAAC,EAAC,sEAAsE;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAC5JjB,OAAA;IAAMS,CAAC,EAAC,qGAAqG;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACxL,CACN;AAACO,GAAA,GAVWD,OAAO;AAYpB,OAAO,MAAME,iBAAiB,GAAGA,CAAC;EAAEvB,KAAK,GAAG;AAAU,CAAC,kBACrDF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,sKAAsK;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAC5PjB,OAAA;IAAMS,CAAC,EAAC,8GAA8G;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACpMjB,OAAA;IAAMS,CAAC,EAAC,oGAAoG;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAC1LjB,OAAA;IAAMS,CAAC,EAAC,+LAA+L;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAClR,CACN;AAACS,GAAA,GAPWD,iBAAiB;AAS9B,OAAO,MAAME,cAAc,GAAGA,CAAC;EAAEzB,KAAK,GAAG;AAAU,CAAC,kBAClDF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,kBAAkB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACxGjB,OAAA;IAAMS,CAAC,EAAC,mBAAmB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACzGjB,OAAA;IAAMS,CAAC,EAAC,qBAAqB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAC3GjB,OAAA;IAAMS,CAAC,EAAC,oBAAoB;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACvG,CACN;AAACW,GAAA,GAPWD,cAAc;AAS3B,OAAO,MAAME,aAAa,GAAGA,CAAC;EAAE3B,KAAK,GAAG;AAAU,CAAC,kBACjDF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,0HAA0H;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAChNjB,OAAA;IAAMS,CAAC,EAAC,kKAAkK;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACrP,CACN;AAACa,GAAA,GALWD,aAAa;AAO1B,OAAO,MAAME,UAAU,GAAGA,CAAC;EAAE7B,KAAK,GAAG;AAAU,CAAC,kBAC9CF,OAAA;EAAKG,KAAK,EAAC,IAAI;EAACC,MAAM,EAAC,IAAI;EAACC,OAAO,EAAC,WAAW;EAACC,IAAI,EAAC,MAAM;EAACC,KAAK,EAAC,4BAA4B;EAAAC,QAAA,gBAC5FR,OAAA;IAAMS,CAAC,EAAC,mHAAmH;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eACzMjB,OAAA;IAAMS,CAAC,EAAC,UAAU;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC,eAChGjB,OAAA;IAAMS,CAAC,EAAC,8JAA8J;IAACC,MAAM,EAAER,KAAM;IAACS,WAAW,EAAC,GAAG;IAACC,aAAa,EAAC,OAAO;IAACC,cAAc,EAAC;EAAO;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAC,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACjP,CACN;AAACe,GAAA,GANWD,UAAU;AAAA,IAAAb,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}