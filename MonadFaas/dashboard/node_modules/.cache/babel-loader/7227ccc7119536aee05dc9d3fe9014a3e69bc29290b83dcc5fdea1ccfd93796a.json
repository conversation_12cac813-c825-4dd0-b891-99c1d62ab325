{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/DashboardModel.tsx\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst float = keyframes`\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n`;\nconst rotate = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\nconst rotateReverse = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(-360deg);\n  }\n`;\nconst glow = keyframes`\n  0%, 100% {\n    filter: drop-shadow(0 0 15px rgba(138, 112, 255, 0.4)) drop-shadow(0 0 30px rgba(138, 112, 255, 0.2));\n  }\n  50% {\n    filter: drop-shadow(0 0 25px rgba(138, 112, 255, 0.7)) drop-shadow(0 0 50px rgba(138, 112, 255, 0.5));\n  }\n`;\nconst pulse = keyframes`\n  0%, 100% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n  }\n`;\nconst scanline = keyframes`\n  0% {\n    transform: translateY(-100%);\n  }\n  100% {\n    transform: translateY(100%);\n  }\n`;\nconst data = keyframes`\n  0%, 100% {\n    height: 30%;\n  }\n  25% {\n    height: 60%;\n  }\n  50% {\n    height: 40%;\n  }\n  75% {\n    height: 70%;\n  }\n`;\nconst ModelContainer = styled.div`\n  position: relative;\n  width: 550px;\n  height: 550px;\n  animation: ${float} 6s ease-in-out infinite;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 350px;\n    height: 350px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    display: none;\n  }\n`;\n_c = ModelContainer;\nconst RotatingElement = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  animation: ${rotate} 25s linear infinite;\n`;\n_c2 = RotatingElement;\nconst FloatingCircle = styled.div`\n  position: absolute;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: ${props => props.color};\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  opacity: 0.7;\n  filter: blur(10px);\n  animation: ${float} ${props => props.duration}s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n  z-index: -1;\n`;\n_c3 = FloatingCircle;\nconst OrbitalRing = styled.div`\n  position: absolute;\n  width: ${props => props.size}%;\n  height: ${props => props.size}%;\n  border-radius: 50%;\n  border: ${props => props.borderWidth}px solid rgba(138, 112, 255, 0.15);\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  animation: ${rotate} ${props => props.rotationSpeed}s linear infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n_c4 = OrbitalRing;\nconst OrbitalObject = styled.div`\n  position: absolute;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: ${props => props.color};\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%) rotate(${props => props.position}deg) translateY(-150%) rotate(-${props => props.position}deg);\n  box-shadow: 0 0 15px ${props => props.color};\n  animation: ${pulse} 3s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n_c5 = OrbitalObject;\nconst DashboardFrame = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  perspective: 1200px;\n  transform-style: preserve-3d;\n  transform: rotateX(15deg) rotateY(-20deg);\n`;\n_c6 = DashboardFrame;\nconst GlowingScreen = styled.div`\n  position: absolute;\n  width: 85%;\n  height: 65%;\n  top: 15%;\n  left: 8%;\n  background: linear-gradient(135deg, #2d1b69 0%, #1e123d 100%);\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 0 30px rgba(138, 112, 255, 0.5), \n              inset 0 0 30px rgba(0, 0, 0, 0.5);\n  animation: ${glow} 4s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: linear-gradient(to right, transparent, ${theme.colors.secondary}, transparent);\n    animation: ${pulse} 2s ease-in-out infinite;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: radial-gradient(circle at 30% 40%, rgba(138, 112, 255, 0.2) 0%, transparent 70%);\n  }\n`;\n_c7 = GlowingScreen;\nconst ScreenBorder = styled.div`\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  border-radius: 18px;\n  border: 2px solid rgba(138, 112, 255, 0.2);\n  pointer-events: none;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    left: -5px;\n    right: -5px;\n    bottom: -5px;\n    border-radius: 22px;\n    border: 1px solid rgba(138, 112, 255, 0.1);\n  }\n`;\n_c8 = ScreenBorder;\nconst Scanline = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 20px;\n  background: linear-gradient(to bottom, \n    rgba(138, 112, 255, 0.1) 0%, \n    rgba(138, 112, 255, 0.5) 50%, \n    rgba(138, 112, 255, 0.1) 100%);\n  opacity: 0.3;\n  animation: ${scanline} 4s linear infinite;\n  pointer-events: none;\n  z-index: 5;\n`;\n_c9 = Scanline;\nconst GridLines = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: \n    linear-gradient(to right, rgba(138, 112, 255, 0.1) 1px, transparent 1px),\n    linear-gradient(to bottom, rgba(138, 112, 255, 0.1) 1px, transparent 1px);\n  background-size: 20px 20px;\n  opacity: 0.4;\n`;\n_c0 = GridLines;\nconst Chart = styled.div`\n  position: absolute;\n  bottom: 10%;\n  left: 5%;\n  right: 5%;\n  height: 35%;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 2px;\n    background-color: rgba(255, 255, 255, 0.2);\n  }\n`;\n_c1 = Chart;\nconst ChartLine = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 60%;\n  background: linear-gradient(to right, \n    ${theme.colors.primary}00 0%, \n    ${theme.colors.primary}40 20%, \n    ${theme.colors.secondary}80 50%, \n    ${theme.colors.primary}40 80%, \n    ${theme.colors.primary}00 100%\n  );\n  clip-path: polygon(\n    0% 100%, \n    5% 80%, \n    15% 60%, \n    25% 80%, \n    35% 40%, \n    45% 60%, \n    55% 20%, \n    65% 40%, \n    75% 10%, \n    85% 30%, \n    95% 20%, \n    100% 40%, \n    100% 100%\n  );\n  opacity: 0.8;\n`;\n_c10 = ChartLine;\nconst DataColumns = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  padding: 0 5%;\n`;\n_c11 = DataColumns;\nconst DataColumn = styled.div`\n  width: 4px;\n  height: ${props => props.height}%;\n  background: linear-gradient(to top, ${props => props.color} 0%, transparent 100%);\n  border-radius: 4px;\n  animation: ${data} 4s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n_c12 = DataColumn;\nconst DataPoints = styled.div`\n  position: absolute;\n  top: 5%;\n  left: 5%;\n  right: 5%;\n  height: 45%;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n`;\n_c13 = DataPoints;\nconst DataPoint = styled.div`\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background-color: ${props => props.color};\n  box-shadow: 0 0 8px ${props => props.color}aa;\n  animation: ${pulse} 2s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n_c14 = DataPoint;\nconst CodeBlock = styled.div`\n  position: absolute;\n  top: 15%;\n  left: 5%;\n  width: 50%;\n  height: 30%;\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n`;\n_c15 = CodeBlock;\nconst CodeLine = styled.div`\n  height: 4px;\n  width: ${props => `${props.width}%`};\n  background-color: ${props => props.color};\n  border-radius: 2px;\n  animation: ${pulse} 2s ease-in-out infinite;\n  animation-delay: ${props => `${props.delay}s`};\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 4px;\n    height: 4px;\n    border-radius: 50%;\n    background-color: ${props => props.color};\n  }\n`;\n_c16 = CodeLine;\nconst NetworkNodes = styled.div`\n  position: absolute;\n  top: 15%;\n  right: 5%;\n  width: 35%;\n  height: 30%;\n`;\n_c17 = NetworkNodes;\nconst NodeConnection = styled.div`\n  position: absolute;\n  top: ${props => props.y1}%;\n  left: ${props => props.x1}%;\n  width: ${props => Math.sqrt(Math.pow(props.x2 - props.x1, 2) + Math.pow(props.y2 - props.y1, 2))}%;\n  height: 1px;\n  background-color: rgba(138, 112, 255, 0.4);\n  transform-origin: 0 0;\n  transform: rotate(${props => Math.atan2(props.y2 - props.y1, props.x2 - props.x1) * (180 / Math.PI)}deg);\n  animation: ${pulse} 3s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n_c18 = NodeConnection;\nconst StatusBar = styled.div`\n  position: absolute;\n  bottom: 5%;\n  left: 5%;\n  right: 5%;\n  height: 5px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 3px;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 70%;\n    height: 100%;\n    background: linear-gradient(to right, ${theme.colors.secondary}aa, ${theme.colors.secondary}55);\n    border-radius: 3px;\n  }\n`;\n_c19 = StatusBar;\nconst DashboardModel = () => {\n  const generateDataPoints = () => {\n    const points = [];\n    const colors = ['#8a70ff', '#00d2c2', '#a170ff', '#00c2c2'];\n    const sizes = [6, 8, 10, 12];\n    for (let i = 0; i < 25; i++) {\n      points.push(/*#__PURE__*/_jsxDEV(DataPoint, {\n        delay: i * 0.1,\n        color: colors[i % colors.length],\n        size: sizes[i % sizes.length]\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this));\n    }\n    return points;\n  };\n  const generateCodeLines = () => {\n    const lines = [];\n    const widths = [70, 50, 80, 60, 40, 90, 45, 75];\n    const colors = ['rgba(138, 112, 255, 0.5)', 'rgba(0, 210, 194, 0.5)'];\n    for (let i = 0; i < 8; i++) {\n      lines.push(/*#__PURE__*/_jsxDEV(CodeLine, {\n        width: widths[i],\n        delay: i * 0.15,\n        color: colors[i % colors.length]\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 9\n      }, this));\n    }\n    return lines;\n  };\n  const generateDataColumns = () => {\n    const columns = [];\n    const heights = [40, 60, 30, 70, 50, 80, 45, 55, 65, 35];\n    const colors = ['#8a70ff', '#00d2c2'];\n    for (let i = 0; i < 10; i++) {\n      columns.push(/*#__PURE__*/_jsxDEV(DataColumn, {\n        height: heights[i],\n        delay: i * 0.2,\n        color: colors[i % colors.length]\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this));\n    }\n    return columns;\n  };\n  const generateNodeConnections = () => {\n    const connections = [];\n    const nodes = [{\n      x: 10,\n      y: 20\n    }, {\n      x: 80,\n      y: 30\n    }, {\n      x: 50,\n      y: 70\n    }, {\n      x: 20,\n      y: 80\n    }, {\n      x: 90,\n      y: 60\n    }];\n    for (let i = 0; i < nodes.length; i++) {\n      for (let j = i + 1; j < nodes.length; j++) {\n        connections.push(/*#__PURE__*/_jsxDEV(NodeConnection, {\n          x1: nodes[i].x,\n          y1: nodes[i].y,\n          x2: nodes[j].x,\n          y2: nodes[j].y,\n          delay: (i + j) * 0.2\n        }, `${i}-${j}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this));\n      }\n    }\n    return connections;\n  };\n  const generateFloatingCircles = () => {\n    const circles = [{\n      size: 150,\n      delay: 0,\n      duration: 7,\n      top: 5,\n      left: 5,\n      color: 'rgba(0, 210, 194, 0.2)'\n    }, {\n      size: 100,\n      delay: 1,\n      duration: 5,\n      top: 70,\n      left: 10,\n      color: 'rgba(138, 112, 255, 0.2)'\n    }, {\n      size: 120,\n      delay: 2,\n      duration: 8,\n      top: 20,\n      left: 80,\n      color: 'rgba(138, 112, 255, 0.15)'\n    }, {\n      size: 80,\n      delay: 3,\n      duration: 6,\n      top: 60,\n      left: 75,\n      color: 'rgba(0, 210, 194, 0.15)'\n    }];\n    return circles.map((circle, index) => /*#__PURE__*/_jsxDEV(FloatingCircle, {\n      size: circle.size,\n      delay: circle.delay,\n      duration: circle.duration,\n      top: circle.top,\n      left: circle.left,\n      color: circle.color\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this));\n  };\n  const generateOrbitalRings = () => {\n    const rings = [{\n      size: 90,\n      borderWidth: 1,\n      rotationSpeed: 30,\n      delay: 0\n    }, {\n      size: 70,\n      borderWidth: 2,\n      rotationSpeed: 20,\n      delay: 1\n    }, {\n      size: 50,\n      borderWidth: 1,\n      rotationSpeed: 15,\n      delay: 2\n    }];\n    return rings.map((ring, index) => /*#__PURE__*/_jsxDEV(OrbitalRing, {\n      size: ring.size,\n      borderWidth: ring.borderWidth,\n      rotationSpeed: ring.rotationSpeed,\n      delay: ring.delay\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 520,\n      columnNumber: 7\n    }, this));\n  };\n  const generateOrbitalObjects = () => {\n    const objects = [{\n      size: 8,\n      color: '#8a70ff',\n      position: 45,\n      delay: 0\n    }, {\n      size: 6,\n      color: '#00d2c2',\n      position: 135,\n      delay: 0.5\n    }, {\n      size: 10,\n      color: '#8a70ff',\n      position: 225,\n      delay: 1\n    }, {\n      size: 5,\n      color: '#00d2c2',\n      position: 315,\n      delay: 1.5\n    }];\n    return objects.map((object, index) => /*#__PURE__*/_jsxDEV(OrbitalObject, {\n      size: object.size,\n      color: object.color,\n      position: object.position,\n      delay: object.delay\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this));\n  };\n  return /*#__PURE__*/_jsxDEV(ModelContainer, {\n    children: [generateFloatingCircles(), /*#__PURE__*/_jsxDEV(RotatingElement, {\n      children: [generateOrbitalRings(), generateOrbitalObjects(), /*#__PURE__*/_jsxDEV(DashboardFrame, {\n        children: /*#__PURE__*/_jsxDEV(GlowingScreen, {\n          children: [/*#__PURE__*/_jsxDEV(ScreenBorder, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(GridLines, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Scanline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 559,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CodeBlock, {\n            children: generateCodeLines()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NetworkNodes, {\n            children: generateNodeConnections()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(DataPoints, {\n            children: generateDataPoints()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Chart, {\n            children: [/*#__PURE__*/_jsxDEV(ChartLine, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(DataColumns, {\n              children: generateDataColumns()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatusBar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 552,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 550,\n    columnNumber: 5\n  }, this);\n};\n_c20 = DashboardModel;\nexport default DashboardModel;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20;\n$RefreshReg$(_c, \"ModelContainer\");\n$RefreshReg$(_c2, \"RotatingElement\");\n$RefreshReg$(_c3, \"FloatingCircle\");\n$RefreshReg$(_c4, \"OrbitalRing\");\n$RefreshReg$(_c5, \"OrbitalObject\");\n$RefreshReg$(_c6, \"DashboardFrame\");\n$RefreshReg$(_c7, \"GlowingScreen\");\n$RefreshReg$(_c8, \"ScreenBorder\");\n$RefreshReg$(_c9, \"Scanline\");\n$RefreshReg$(_c0, \"GridLines\");\n$RefreshReg$(_c1, \"Chart\");\n$RefreshReg$(_c10, \"ChartLine\");\n$RefreshReg$(_c11, \"DataColumns\");\n$RefreshReg$(_c12, \"DataColumn\");\n$RefreshReg$(_c13, \"DataPoints\");\n$RefreshReg$(_c14, \"DataPoint\");\n$RefreshReg$(_c15, \"CodeBlock\");\n$RefreshReg$(_c16, \"CodeLine\");\n$RefreshReg$(_c17, \"NetworkNodes\");\n$RefreshReg$(_c18, \"NodeConnection\");\n$RefreshReg$(_c19, \"StatusBar\");\n$RefreshReg$(_c20, \"DashboardModel\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "theme", "jsxDEV", "_jsxDEV", "float", "rotate", "rotateReverse", "glow", "pulse", "scanline", "data", "ModelContainer", "div", "breakpoints", "md", "sm", "_c", "RotatingElement", "_c2", "FloatingCircle", "props", "size", "color", "top", "left", "duration", "delay", "_c3", "OrbitalRing", "borderWidth", "rotationSpeed", "_c4", "OrbitalObject", "position", "_c5", "DashboardFrame", "_c6", "GlowingScreen", "colors", "secondary", "_c7", "ScreenBorder", "_c8", "Scanline", "_c9", "GridLines", "_c0", "Chart", "_c1", "ChartLine", "primary", "_c10", "DataColumns", "_c11", "DataColumn", "height", "_c12", "DataPoints", "_c13", "DataPoint", "_c14", "CodeBlock", "_c15", "CodeLine", "width", "_c16", "NetworkNodes", "_c17", "NodeConnection", "y1", "x1", "Math", "sqrt", "pow", "x2", "y2", "atan2", "PI", "_c18", "StatusBar", "_c19", "DashboardModel", "generateDataPoints", "points", "sizes", "i", "push", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "generateCodeLines", "lines", "widths", "generateDataColumns", "columns", "heights", "generateNodeConnections", "connections", "nodes", "x", "y", "j", "generateFloatingCircles", "circles", "map", "circle", "index", "generateOrbitalRings", "rings", "ring", "generateOrbitalObjects", "objects", "object", "children", "_c20", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/DashboardModel.tsx"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\nconst float = keyframes`\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n`;\n\nconst rotate = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\n\nconst rotateReverse = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(-360deg);\n  }\n`;\n\nconst glow = keyframes`\n  0%, 100% {\n    filter: drop-shadow(0 0 15px rgba(138, 112, 255, 0.4)) drop-shadow(0 0 30px rgba(138, 112, 255, 0.2));\n  }\n  50% {\n    filter: drop-shadow(0 0 25px rgba(138, 112, 255, 0.7)) drop-shadow(0 0 50px rgba(138, 112, 255, 0.5));\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    opacity: 0.6;\n  }\n  50% {\n    opacity: 1;\n  }\n`;\n\nconst scanline = keyframes`\n  0% {\n    transform: translateY(-100%);\n  }\n  100% {\n    transform: translateY(100%);\n  }\n`;\n\nconst data = keyframes`\n  0%, 100% {\n    height: 30%;\n  }\n  25% {\n    height: 60%;\n  }\n  50% {\n    height: 40%;\n  }\n  75% {\n    height: 70%;\n  }\n`;\n\nconst ModelContainer = styled.div`\n  position: relative;\n  width: 550px;\n  height: 550px;\n  animation: ${float} 6s ease-in-out infinite;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    width: 350px;\n    height: 350px;\n  }\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    display: none;\n  }\n`;\n\nconst RotatingElement = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  animation: ${rotate} 25s linear infinite;\n`;\n\nconst FloatingCircle = styled.div<{ size: number; delay: number; duration: number; top: number; left: number; color: string }>`\n  position: absolute;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: ${props => props.color};\n  top: ${props => props.top}%;\n  left: ${props => props.left}%;\n  opacity: 0.7;\n  filter: blur(10px);\n  animation: ${float} ${props => props.duration}s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n  z-index: -1;\n`;\n\nconst OrbitalRing = styled.div<{ size: number; borderWidth: number; rotationSpeed: number; delay: number }>`\n  position: absolute;\n  width: ${props => props.size}%;\n  height: ${props => props.size}%;\n  border-radius: 50%;\n  border: ${props => props.borderWidth}px solid rgba(138, 112, 255, 0.15);\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  animation: ${rotate} ${props => props.rotationSpeed}s linear infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n\nconst OrbitalObject = styled.div<{ size: number; color: string; position: number; delay: number }>`\n  position: absolute;\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background: ${props => props.color};\n  top: 0;\n  left: 50%;\n  transform: translateX(-50%) rotate(${props => props.position}deg) translateY(-150%) rotate(-${props => props.position}deg);\n  box-shadow: 0 0 15px ${props => props.color};\n  animation: ${pulse} 3s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n\nconst DashboardFrame = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  perspective: 1200px;\n  transform-style: preserve-3d;\n  transform: rotateX(15deg) rotateY(-20deg);\n`;\n\nconst GlowingScreen = styled.div`\n  position: absolute;\n  width: 85%;\n  height: 65%;\n  top: 15%;\n  left: 8%;\n  background: linear-gradient(135deg, #2d1b69 0%, #1e123d 100%);\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 0 30px rgba(138, 112, 255, 0.5), \n              inset 0 0 30px rgba(0, 0, 0, 0.5);\n  animation: ${glow} 4s ease-in-out infinite;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: linear-gradient(to right, transparent, ${theme.colors.secondary}, transparent);\n    animation: ${pulse} 2s ease-in-out infinite;\n  }\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: radial-gradient(circle at 30% 40%, rgba(138, 112, 255, 0.2) 0%, transparent 70%);\n  }\n`;\n\nconst ScreenBorder = styled.div`\n  position: absolute;\n  top: -10px;\n  left: -10px;\n  right: -10px;\n  bottom: -10px;\n  border-radius: 18px;\n  border: 2px solid rgba(138, 112, 255, 0.2);\n  pointer-events: none;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    left: -5px;\n    right: -5px;\n    bottom: -5px;\n    border-radius: 22px;\n    border: 1px solid rgba(138, 112, 255, 0.1);\n  }\n`;\n\nconst Scanline = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 20px;\n  background: linear-gradient(to bottom, \n    rgba(138, 112, 255, 0.1) 0%, \n    rgba(138, 112, 255, 0.5) 50%, \n    rgba(138, 112, 255, 0.1) 100%);\n  opacity: 0.3;\n  animation: ${scanline} 4s linear infinite;\n  pointer-events: none;\n  z-index: 5;\n`;\n\nconst GridLines = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: \n    linear-gradient(to right, rgba(138, 112, 255, 0.1) 1px, transparent 1px),\n    linear-gradient(to bottom, rgba(138, 112, 255, 0.1) 1px, transparent 1px);\n  background-size: 20px 20px;\n  opacity: 0.4;\n`;\n\nconst Chart = styled.div`\n  position: absolute;\n  bottom: 10%;\n  left: 5%;\n  right: 5%;\n  height: 35%;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    height: 2px;\n    background-color: rgba(255, 255, 255, 0.2);\n  }\n`;\n\nconst ChartLine = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 60%;\n  background: linear-gradient(to right, \n    ${theme.colors.primary}00 0%, \n    ${theme.colors.primary}40 20%, \n    ${theme.colors.secondary}80 50%, \n    ${theme.colors.primary}40 80%, \n    ${theme.colors.primary}00 100%\n  );\n  clip-path: polygon(\n    0% 100%, \n    5% 80%, \n    15% 60%, \n    25% 80%, \n    35% 40%, \n    45% 60%, \n    55% 20%, \n    65% 40%, \n    75% 10%, \n    85% 30%, \n    95% 20%, \n    100% 40%, \n    100% 100%\n  );\n  opacity: 0.8;\n`;\n\nconst DataColumns = styled.div`\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-end;\n  padding: 0 5%;\n`;\n\nconst DataColumn = styled.div<{ height: number; delay: number; color: string }>`\n  width: 4px;\n  height: ${props => props.height}%;\n  background: linear-gradient(to top, ${props => props.color} 0%, transparent 100%);\n  border-radius: 4px;\n  animation: ${data} 4s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n\nconst DataPoints = styled.div`\n  position: absolute;\n  top: 5%;\n  left: 5%;\n  right: 5%;\n  height: 45%;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n`;\n\nconst DataPoint = styled.div<{ delay: number; color: string; size: number }>`\n  width: ${props => props.size}px;\n  height: ${props => props.size}px;\n  border-radius: 50%;\n  background-color: ${props => props.color};\n  box-shadow: 0 0 8px ${props => props.color}aa;\n  animation: ${pulse} 2s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n\nconst CodeBlock = styled.div`\n  position: absolute;\n  top: 15%;\n  left: 5%;\n  width: 50%;\n  height: 30%;\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n`;\n\nconst CodeLine = styled.div<{ width: number; delay: number; color: string }>`\n  height: 4px;\n  width: ${props => `${props.width}%`};\n  background-color: ${props => props.color};\n  border-radius: 2px;\n  animation: ${pulse} 2s ease-in-out infinite;\n  animation-delay: ${props => `${props.delay}s`};\n  position: relative;\n  \n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    width: 4px;\n    height: 4px;\n    border-radius: 50%;\n    background-color: ${props => props.color};\n  }\n`;\n\nconst NetworkNodes = styled.div`\n  position: absolute;\n  top: 15%;\n  right: 5%;\n  width: 35%;\n  height: 30%;\n`;\n\nconst NodeConnection = styled.div<{ x1: number; y1: number; x2: number; y2: number; delay: number }>`\n  position: absolute;\n  top: ${props => props.y1}%;\n  left: ${props => props.x1}%;\n  width: ${props => Math.sqrt(Math.pow(props.x2 - props.x1, 2) + Math.pow(props.y2 - props.y1, 2))}%;\n  height: 1px;\n  background-color: rgba(138, 112, 255, 0.4);\n  transform-origin: 0 0;\n  transform: rotate(${props => Math.atan2(props.y2 - props.y1, props.x2 - props.x1) * (180 / Math.PI)}deg);\n  animation: ${pulse} 3s ease-in-out infinite;\n  animation-delay: ${props => props.delay}s;\n`;\n\nconst StatusBar = styled.div`\n  position: absolute;\n  bottom: 5%;\n  left: 5%;\n  right: 5%;\n  height: 5px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 3px;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 70%;\n    height: 100%;\n    background: linear-gradient(to right, ${theme.colors.secondary}aa, ${theme.colors.secondary}55);\n    border-radius: 3px;\n  }\n`;\n\nconst DashboardModel: React.FC = () => {\n  const generateDataPoints = () => {\n    const points = [];\n    const colors = ['#8a70ff', '#00d2c2', '#a170ff', '#00c2c2'];\n    const sizes = [6, 8, 10, 12];\n    \n    for (let i = 0; i < 25; i++) {\n      points.push(\n        <DataPoint \n          key={i} \n          delay={i * 0.1} \n          color={colors[i % colors.length]} \n          size={sizes[i % sizes.length]}\n        />\n      );\n    }\n    \n    return points;\n  };\n  \n  const generateCodeLines = () => {\n    const lines = [];\n    const widths = [70, 50, 80, 60, 40, 90, 45, 75];\n    const colors = ['rgba(138, 112, 255, 0.5)', 'rgba(0, 210, 194, 0.5)'];\n    \n    for (let i = 0; i < 8; i++) {\n      lines.push(\n        <CodeLine \n          key={i} \n          width={widths[i]} \n          delay={i * 0.15} \n          color={colors[i % colors.length]}\n        />\n      );\n    }\n    \n    return lines;\n  };\n  \n  const generateDataColumns = () => {\n    const columns = [];\n    const heights = [40, 60, 30, 70, 50, 80, 45, 55, 65, 35];\n    const colors = ['#8a70ff', '#00d2c2'];\n    \n    for (let i = 0; i < 10; i++) {\n      columns.push(\n        <DataColumn\n          key={i}\n          height={heights[i]}\n          delay={i * 0.2}\n          color={colors[i % colors.length]}\n        />\n      );\n    }\n    \n    return columns;\n  };\n  \n  const generateNodeConnections = () => {\n    const connections = [];\n    const nodes = [\n      { x: 10, y: 20 },\n      { x: 80, y: 30 },\n      { x: 50, y: 70 },\n      { x: 20, y: 80 },\n      { x: 90, y: 60 }\n    ];\n    \n    for (let i = 0; i < nodes.length; i++) {\n      for (let j = i + 1; j < nodes.length; j++) {\n        connections.push(\n          <NodeConnection\n            key={`${i}-${j}`}\n            x1={nodes[i].x}\n            y1={nodes[i].y}\n            x2={nodes[j].x}\n            y2={nodes[j].y}\n            delay={(i + j) * 0.2}\n          />\n        );\n      }\n    }\n    \n    return connections;\n  };\n  \n  const generateFloatingCircles = () => {\n    const circles = [\n      { size: 150, delay: 0, duration: 7, top: 5, left: 5, color: 'rgba(0, 210, 194, 0.2)' },\n      { size: 100, delay: 1, duration: 5, top: 70, left: 10, color: 'rgba(138, 112, 255, 0.2)' },\n      { size: 120, delay: 2, duration: 8, top: 20, left: 80, color: 'rgba(138, 112, 255, 0.15)' },\n      { size: 80, delay: 3, duration: 6, top: 60, left: 75, color: 'rgba(0, 210, 194, 0.15)' }\n    ];\n    \n    return circles.map((circle, index) => (\n      <FloatingCircle\n        key={index}\n        size={circle.size}\n        delay={circle.delay}\n        duration={circle.duration}\n        top={circle.top}\n        left={circle.left}\n        color={circle.color}\n      />\n    ));\n  };\n  \n  const generateOrbitalRings = () => {\n    const rings = [\n      { size: 90, borderWidth: 1, rotationSpeed: 30, delay: 0 },\n      { size: 70, borderWidth: 2, rotationSpeed: 20, delay: 1 },\n      { size: 50, borderWidth: 1, rotationSpeed: 15, delay: 2 }\n    ];\n    \n    return rings.map((ring, index) => (\n      <OrbitalRing\n        key={index}\n        size={ring.size}\n        borderWidth={ring.borderWidth}\n        rotationSpeed={ring.rotationSpeed}\n        delay={ring.delay}\n      />\n    ));\n  };\n  \n  const generateOrbitalObjects = () => {\n    const objects = [\n      { size: 8, color: '#8a70ff', position: 45, delay: 0 },\n      { size: 6, color: '#00d2c2', position: 135, delay: 0.5 },\n      { size: 10, color: '#8a70ff', position: 225, delay: 1 },\n      { size: 5, color: '#00d2c2', position: 315, delay: 1.5 }\n    ];\n    \n    return objects.map((object, index) => (\n      <OrbitalObject\n        key={index}\n        size={object.size}\n        color={object.color}\n        position={object.position}\n        delay={object.delay}\n      />\n    ));\n  };\n  \n  return (\n    <ModelContainer>\n      {generateFloatingCircles()}\n      <RotatingElement>\n        {generateOrbitalRings()}\n        {generateOrbitalObjects()}\n        <DashboardFrame>\n          <GlowingScreen>\n            <ScreenBorder />\n            <GridLines />\n            <Scanline />\n            <CodeBlock>\n              {generateCodeLines()}\n            </CodeBlock>\n            <NetworkNodes>\n              {generateNodeConnections()}\n            </NetworkNodes>\n            <DataPoints>\n              {generateDataPoints()}\n            </DataPoints>\n            <Chart>\n              <ChartLine />\n              <DataColumns>\n                {generateDataColumns()}\n              </DataColumns>\n            </Chart>\n            <StatusBar />\n          </GlowingScreen>\n        </DashboardFrame>\n      </RotatingElement>\n    </ModelContainer>\n  );\n};\n\nexport default DashboardModel; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,KAAK,GAAGJ,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMK,MAAM,GAAGL,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,aAAa,GAAGN,SAAS;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,IAAI,GAAGP,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMQ,KAAK,GAAGR,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMS,QAAQ,GAAGT,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMU,IAAI,GAAGV,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMW,cAAc,GAAGZ,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA,eAAeR,KAAK;AACpB;AACA,uBAAuBH,KAAK,CAACY,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA;AACA,uBAAuBb,KAAK,CAACY,WAAW,CAACE,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,EAAA,GAdIL,cAAc;AAgBpB,MAAMM,eAAe,GAAGlB,MAAM,CAACa,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,eAAeP,MAAM;AACrB,CAAC;AAACa,GAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAGpB,MAAM,CAACa,GAAgG;AAC9H;AACA,WAAWQ,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC9B,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC/B;AACA,gBAAgBD,KAAK,IAAIA,KAAK,CAACE,KAAK;AACpC,SAASF,KAAK,IAAIA,KAAK,CAACG,GAAG;AAC3B,UAAUH,KAAK,IAAIA,KAAK,CAACI,IAAI;AAC7B;AACA;AACA,eAAepB,KAAK,IAAIgB,KAAK,IAAIA,KAAK,CAACK,QAAQ;AAC/C,qBAAqBL,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC;AACA,CAAC;AAACC,GAAA,GAbIR,cAAc;AAepB,MAAMS,WAAW,GAAG7B,MAAM,CAACa,GAAgF;AAC3G;AACA,WAAWQ,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC9B,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC/B;AACA,YAAYD,KAAK,IAAIA,KAAK,CAACS,WAAW;AACtC;AACA;AACA;AACA,eAAexB,MAAM,IAAIe,KAAK,IAAIA,KAAK,CAACU,aAAa;AACrD,qBAAqBV,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC,CAAC;AAACK,GAAA,GAXIH,WAAW;AAajB,MAAMI,aAAa,GAAGjC,MAAM,CAACa,GAAqE;AAClG;AACA,WAAWQ,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC9B,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC/B;AACA,gBAAgBD,KAAK,IAAIA,KAAK,CAACE,KAAK;AACpC;AACA;AACA,uCAAuCF,KAAK,IAAIA,KAAK,CAACa,QAAQ,kCAAkCb,KAAK,IAAIA,KAAK,CAACa,QAAQ;AACvH,yBAAyBb,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC7C,eAAed,KAAK;AACpB,qBAAqBY,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC,CAAC;AAACQ,GAAA,GAZIF,aAAa;AAcnB,MAAMG,cAAc,GAAGpC,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAPID,cAAc;AASpB,MAAME,aAAa,GAAGtC,MAAM,CAACa,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeL,IAAI;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyDN,KAAK,CAACqC,MAAM,CAACC,SAAS;AAC/E,iBAAiB/B,KAAK;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,GAAA,GAjCIH,aAAa;AAmCnB,MAAMI,YAAY,GAAG1C,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8B,GAAA,GApBID,YAAY;AAsBlB,MAAME,QAAQ,GAAG5C,MAAM,CAACa,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,QAAQ;AACvB;AACA;AACA,CAAC;AAACmC,GAAA,GAdID,QAAQ;AAgBd,MAAME,SAAS,GAAG9C,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,GAAA,GAXID,SAAS;AAaf,MAAME,KAAK,GAAGhD,MAAM,CAACa,GAAG;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,GAAA,GAhBID,KAAK;AAkBX,MAAME,SAAS,GAAGlD,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMX,KAAK,CAACqC,MAAM,CAACY,OAAO;AAC1B,MAAMjD,KAAK,CAACqC,MAAM,CAACY,OAAO;AAC1B,MAAMjD,KAAK,CAACqC,MAAM,CAACC,SAAS;AAC5B,MAAMtC,KAAK,CAACqC,MAAM,CAACY,OAAO;AAC1B,MAAMjD,KAAK,CAACqC,MAAM,CAACY,OAAO;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7BIF,SAAS;AA+Bf,MAAMG,WAAW,GAAGrD,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyC,IAAA,GAVID,WAAW;AAYjB,MAAME,UAAU,GAAGvD,MAAM,CAACa,GAAqD;AAC/E;AACA,YAAYQ,KAAK,IAAIA,KAAK,CAACmC,MAAM;AACjC,wCAAwCnC,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC5D;AACA,eAAeZ,IAAI;AACnB,qBAAqBU,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC,CAAC;AAAC8B,IAAA,GAPIF,UAAU;AAShB,MAAMG,UAAU,GAAG1D,MAAM,CAACa,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC8C,IAAA,GATID,UAAU;AAWhB,MAAME,SAAS,GAAG5D,MAAM,CAACa,GAAmD;AAC5E,WAAWQ,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC9B,YAAYD,KAAK,IAAIA,KAAK,CAACC,IAAI;AAC/B;AACA,sBAAsBD,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC1C,wBAAwBF,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC5C,eAAed,KAAK;AACpB,qBAAqBY,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC,CAAC;AAACkC,IAAA,GARID,SAAS;AAUf,MAAME,SAAS,GAAG9D,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkD,IAAA,GATID,SAAS;AAWf,MAAME,QAAQ,GAAGhE,MAAM,CAACa,GAAoD;AAC5E;AACA,WAAWQ,KAAK,IAAI,GAAGA,KAAK,CAAC4C,KAAK,GAAG;AACrC,sBAAsB5C,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC1C;AACA,eAAed,KAAK;AACpB,qBAAqBY,KAAK,IAAI,GAAGA,KAAK,CAACM,KAAK,GAAG;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBN,KAAK,IAAIA,KAAK,CAACE,KAAK;AAC5C;AACA,CAAC;AAAC2C,IAAA,GAnBIF,QAAQ;AAqBd,MAAMG,YAAY,GAAGnE,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuD,IAAA,GANID,YAAY;AAQlB,MAAME,cAAc,GAAGrE,MAAM,CAACa,GAAsE;AACpG;AACA,SAASQ,KAAK,IAAIA,KAAK,CAACiD,EAAE;AAC1B,UAAUjD,KAAK,IAAIA,KAAK,CAACkD,EAAE;AAC3B,WAAWlD,KAAK,IAAImD,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACrD,KAAK,CAACsD,EAAE,GAAGtD,KAAK,CAACkD,EAAE,EAAE,CAAC,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACrD,KAAK,CAACuD,EAAE,GAAGvD,KAAK,CAACiD,EAAE,EAAE,CAAC,CAAC,CAAC;AAClG;AACA;AACA;AACA,sBAAsBjD,KAAK,IAAImD,IAAI,CAACK,KAAK,CAACxD,KAAK,CAACuD,EAAE,GAAGvD,KAAK,CAACiD,EAAE,EAAEjD,KAAK,CAACsD,EAAE,GAAGtD,KAAK,CAACkD,EAAE,CAAC,IAAI,GAAG,GAAGC,IAAI,CAACM,EAAE,CAAC;AACrG,eAAerE,KAAK;AACpB,qBAAqBY,KAAK,IAAIA,KAAK,CAACM,KAAK;AACzC,CAAC;AAACoD,IAAA,GAXIV,cAAc;AAapB,MAAMW,SAAS,GAAGhF,MAAM,CAACa,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4CX,KAAK,CAACqC,MAAM,CAACC,SAAS,OAAOtC,KAAK,CAACqC,MAAM,CAACC,SAAS;AAC/F;AACA;AACA,CAAC;AAACyC,IAAA,GApBID,SAAS;AAsBf,MAAME,cAAwB,GAAGA,CAAA,KAAM;EACrC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,MAAM,GAAG,EAAE;IACjB,MAAM7C,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;IAC3D,MAAM8C,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAE5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BF,MAAM,CAACG,IAAI,cACTnF,OAAA,CAACwD,SAAS;QAERjC,KAAK,EAAE2D,CAAC,GAAG,GAAI;QACf/D,KAAK,EAAEgB,MAAM,CAAC+C,CAAC,GAAG/C,MAAM,CAACiD,MAAM,CAAE;QACjClE,IAAI,EAAE+D,KAAK,CAACC,CAAC,GAAGD,KAAK,CAACG,MAAM;MAAE,GAHzBF,CAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIP,CACH,CAAC;IACH;IAEA,OAAOR,MAAM;EACf,CAAC;EAED,MAAMS,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC/C,MAAMxD,MAAM,GAAG,CAAC,0BAA0B,EAAE,wBAAwB,CAAC;IAErE,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1BQ,KAAK,CAACP,IAAI,cACRnF,OAAA,CAAC4D,QAAQ;QAEPC,KAAK,EAAE8B,MAAM,CAACT,CAAC,CAAE;QACjB3D,KAAK,EAAE2D,CAAC,GAAG,IAAK;QAChB/D,KAAK,EAAEgB,MAAM,CAAC+C,CAAC,GAAG/C,MAAM,CAACiD,MAAM;MAAE,GAH5BF,CAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIP,CACH,CAAC;IACH;IAEA,OAAOE,KAAK;EACd,CAAC;EAED,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACxD,MAAM3D,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;IAErC,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3BW,OAAO,CAACV,IAAI,cACVnF,OAAA,CAACmD,UAAU;QAETC,MAAM,EAAE0C,OAAO,CAACZ,CAAC,CAAE;QACnB3D,KAAK,EAAE2D,CAAC,GAAG,GAAI;QACf/D,KAAK,EAAEgB,MAAM,CAAC+C,CAAC,GAAG/C,MAAM,CAACiD,MAAM;MAAE,GAH5BF,CAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIP,CACH,CAAC;IACH;IAEA,OAAOK,OAAO;EAChB,CAAC;EAED,MAAME,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,WAAW,GAAG,EAAE;IACtB,MAAMC,KAAK,GAAG,CACZ;MAAEC,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC,EAChB;MAAED,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC,EAChB;MAAED,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC,EAChB;MAAED,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC,EAChB;MAAED,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAG,CAAC,CACjB;IAED,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,KAAK,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;MACrC,KAAK,IAAIkB,CAAC,GAAGlB,CAAC,GAAG,CAAC,EAAEkB,CAAC,GAAGH,KAAK,CAACb,MAAM,EAAEgB,CAAC,EAAE,EAAE;QACzCJ,WAAW,CAACb,IAAI,cACdnF,OAAA,CAACiE,cAAc;UAEbE,EAAE,EAAE8B,KAAK,CAACf,CAAC,CAAC,CAACgB,CAAE;UACfhC,EAAE,EAAE+B,KAAK,CAACf,CAAC,CAAC,CAACiB,CAAE;UACf5B,EAAE,EAAE0B,KAAK,CAACG,CAAC,CAAC,CAACF,CAAE;UACf1B,EAAE,EAAEyB,KAAK,CAACG,CAAC,CAAC,CAACD,CAAE;UACf5E,KAAK,EAAE,CAAC2D,CAAC,GAAGkB,CAAC,IAAI;QAAI,GALhB,GAAGlB,CAAC,IAAIkB,CAAC,EAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMjB,CACH,CAAC;MACH;IACF;IAEA,OAAOQ,WAAW;EACpB,CAAC;EAED,MAAMK,uBAAuB,GAAGA,CAAA,KAAM;IACpC,MAAMC,OAAO,GAAG,CACd;MAAEpF,IAAI,EAAE,GAAG;MAAEK,KAAK,EAAE,CAAC;MAAED,QAAQ,EAAE,CAAC;MAAEF,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEF,KAAK,EAAE;IAAyB,CAAC,EACtF;MAAED,IAAI,EAAE,GAAG;MAAEK,KAAK,EAAE,CAAC;MAAED,QAAQ,EAAE,CAAC;MAAEF,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEF,KAAK,EAAE;IAA2B,CAAC,EAC1F;MAAED,IAAI,EAAE,GAAG;MAAEK,KAAK,EAAE,CAAC;MAAED,QAAQ,EAAE,CAAC;MAAEF,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEF,KAAK,EAAE;IAA4B,CAAC,EAC3F;MAAED,IAAI,EAAE,EAAE;MAAEK,KAAK,EAAE,CAAC;MAAED,QAAQ,EAAE,CAAC;MAAEF,GAAG,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEF,KAAK,EAAE;IAA0B,CAAC,CACzF;IAED,OAAOmF,OAAO,CAACC,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC/BzG,OAAA,CAACgB,cAAc;MAEbE,IAAI,EAAEsF,MAAM,CAACtF,IAAK;MAClBK,KAAK,EAAEiF,MAAM,CAACjF,KAAM;MACpBD,QAAQ,EAAEkF,MAAM,CAAClF,QAAS;MAC1BF,GAAG,EAAEoF,MAAM,CAACpF,GAAI;MAChBC,IAAI,EAAEmF,MAAM,CAACnF,IAAK;MAClBF,KAAK,EAAEqF,MAAM,CAACrF;IAAM,GANfsF,KAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOX,CACF,CAAC;EACJ,CAAC;EAED,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,KAAK,GAAG,CACZ;MAAEzF,IAAI,EAAE,EAAE;MAAEQ,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,EAAE;MAAEJ,KAAK,EAAE;IAAE,CAAC,EACzD;MAAEL,IAAI,EAAE,EAAE;MAAEQ,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,EAAE;MAAEJ,KAAK,EAAE;IAAE,CAAC,EACzD;MAAEL,IAAI,EAAE,EAAE;MAAEQ,WAAW,EAAE,CAAC;MAAEC,aAAa,EAAE,EAAE;MAAEJ,KAAK,EAAE;IAAE,CAAC,CAC1D;IAED,OAAOoF,KAAK,CAACJ,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBAC3BzG,OAAA,CAACyB,WAAW;MAEVP,IAAI,EAAE0F,IAAI,CAAC1F,IAAK;MAChBQ,WAAW,EAAEkF,IAAI,CAAClF,WAAY;MAC9BC,aAAa,EAAEiF,IAAI,CAACjF,aAAc;MAClCJ,KAAK,EAAEqF,IAAI,CAACrF;IAAM,GAJbkF,KAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CACF,CAAC;EACJ,CAAC;EAED,MAAMqB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,OAAO,GAAG,CACd;MAAE5F,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEW,QAAQ,EAAE,EAAE;MAAEP,KAAK,EAAE;IAAE,CAAC,EACrD;MAAEL,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEW,QAAQ,EAAE,GAAG;MAAEP,KAAK,EAAE;IAAI,CAAC,EACxD;MAAEL,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,SAAS;MAAEW,QAAQ,EAAE,GAAG;MAAEP,KAAK,EAAE;IAAE,CAAC,EACvD;MAAEL,IAAI,EAAE,CAAC;MAAEC,KAAK,EAAE,SAAS;MAAEW,QAAQ,EAAE,GAAG;MAAEP,KAAK,EAAE;IAAI,CAAC,CACzD;IAED,OAAOuF,OAAO,CAACP,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,kBAC/BzG,OAAA,CAAC6B,aAAa;MAEZX,IAAI,EAAE6F,MAAM,CAAC7F,IAAK;MAClBC,KAAK,EAAE4F,MAAM,CAAC5F,KAAM;MACpBW,QAAQ,EAAEiF,MAAM,CAACjF,QAAS;MAC1BP,KAAK,EAAEwF,MAAM,CAACxF;IAAM,GAJfkF,KAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAKX,CACF,CAAC;EACJ,CAAC;EAED,oBACExF,OAAA,CAACQ,cAAc;IAAAwG,QAAA,GACZX,uBAAuB,CAAC,CAAC,eAC1BrG,OAAA,CAACc,eAAe;MAAAkG,QAAA,GACbN,oBAAoB,CAAC,CAAC,EACtBG,sBAAsB,CAAC,CAAC,eACzB7G,OAAA,CAACgC,cAAc;QAAAgF,QAAA,eACbhH,OAAA,CAACkC,aAAa;UAAA8E,QAAA,gBACZhH,OAAA,CAACsC,YAAY;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChBxF,OAAA,CAAC0C,SAAS;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACbxF,OAAA,CAACwC,QAAQ;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZxF,OAAA,CAAC0D,SAAS;YAAAsD,QAAA,EACPvB,iBAAiB,CAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACZxF,OAAA,CAAC+D,YAAY;YAAAiD,QAAA,EACVjB,uBAAuB,CAAC;UAAC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACfxF,OAAA,CAACsD,UAAU;YAAA0D,QAAA,EACRjC,kBAAkB,CAAC;UAAC;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACbxF,OAAA,CAAC4C,KAAK;YAAAoE,QAAA,gBACJhH,OAAA,CAAC8C,SAAS;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACbxF,OAAA,CAACiD,WAAW;cAAA+D,QAAA,EACTpB,mBAAmB,CAAC;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACRxF,OAAA,CAAC4E,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAErB,CAAC;AAACyB,IAAA,GAhLInC,cAAwB;AAkL9B,eAAeA,cAAc;AAAC,IAAAjE,EAAA,EAAAE,GAAA,EAAAS,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAW,IAAA,EAAAE,IAAA,EAAAoC,IAAA;AAAAC,YAAA,CAAArG,EAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAvE,GAAA;AAAAuE,YAAA,CAAArE,GAAA;AAAAqE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAhE,IAAA;AAAAgE,YAAA,CAAA7D,IAAA;AAAA6D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAvD,IAAA;AAAAuD,YAAA,CAAApD,IAAA;AAAAoD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAArC,IAAA;AAAAqC,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}