{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Features/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Planet from './Planets/Planet';\nimport FeatureCard from './Cards/FeatureCard';\nimport { theme } from '../../styles/theme';\nimport { TestnetIcon, IntegrationIcon, DevelopmentIcon, RPCIcon, CollaborationIcon, MonitoringIcon, FrameworkIcon, GlobalIcon } from './Icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst glow = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(138, 112, 255, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 15px rgba(138, 112, 255, 0.6);\n  }\n`;\nconst borderGlow = keyframes`\n  0%, 100% {\n    border-color: rgba(138, 112, 255, 0.3);\n  }\n  50% {\n    border-color: rgba(138, 112, 255, 0.6);\n  }\n`;\nconst slideIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\nconst lineGrow = keyframes`\n  from {\n    width: 0;\n  }\n  to {\n    width: 100%;\n  }\n`;\nconst FeaturesSectionContainer = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  padding: 2rem 0 5rem;\n  position: relative;\n  overflow: hidden;\n`;\n_c = FeaturesSectionContainer;\nconst BackgroundPattern = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(rgba(14, 14, 28, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(14, 14, 28, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  z-index: 1;\n  pointer-events: none;\n`;\n_c2 = BackgroundPattern;\nconst ContentContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  position: relative;\n  z-index: 2;\n`;\n_c3 = ContentContainer;\nconst SectionHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  opacity: 0;\n  animation: ${slideIn} 0.8s forwards ease-out;\n`;\n_c4 = SectionHeader;\nconst Title = styled.h2`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1rem;\n  line-height: 1.2;\n  \n  span {\n    color: ${theme.colors.secondary};\n    display: inline-block;\n  }\n`;\n_c5 = Title;\nconst Subtitle = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n`;\n_c6 = Subtitle;\nconst FeaturesContainer = styled.div`\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n`;\n_c7 = FeaturesContainer;\nconst PlanetsContainer = styled.div`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 800px;\n  perspective: 1000px;\n  margin-bottom: 0;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    height: 600px;\n  }\n`;\n_c8 = PlanetsContainer;\nconst PlanetsWrapper = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  transform-style: preserve-3d;\n`;\n_c9 = PlanetsWrapper;\nconst FeatureBoxesGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: repeat(2, auto);\n  gap: 1.5rem;\n  position: relative;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c0 = FeatureBoxesGrid;\nconst FeatureBox = styled.div`\n  position: relative;\n  padding: 1.5rem;\n  background: rgba(14, 14, 28, 0.5);\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.color || 'rgba(138, 112, 255, 0.2)'};\n  border-radius: 8px;\n  animation: ${borderGlow} 4s infinite ease-in-out;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    background: linear-gradient(to bottom right, \n      ${props => props.color}05 0%, \n      transparent 40%, \n      transparent 60%, \n      ${props => props.color}05 100%\n    );\n    opacity: 0.3;\n    z-index: -1;\n  }\n  \n  // Define different corner styles\n  ${props => {\n  switch (props.position) {\n    case 'topleft':\n      return `\n          grid-column: 1;\n          grid-row: 1;\n        `;\n    case 'topright':\n      return `\n          grid-column: 2;\n          grid-row: 1;\n        `;\n    case 'bottomleft':\n      return `\n          grid-column: 1;\n          grid-row: 2;\n        `;\n    case 'bottomright':\n      return `\n          grid-column: 2;\n          grid-row: 2;\n        `;\n    default:\n      return '';\n  }\n}}\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-column: 1;\n  }\n`;\n_c1 = FeatureBox;\nconst FeatureBoxTitle = styled.h3`\n  font-size: 1.5rem;\n  color: ${props => props.color};\n  margin-bottom: 1rem;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  position: relative;\n  display: inline-block;\n`;\n_c10 = FeatureBoxTitle;\nconst FeatureItemsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n`;\n_c11 = FeatureItemsContainer;\nconst BuildPlanetPosition = styled.div`\n  position: absolute;\n  left: 38%;\n  top: 45%;\n  transform: translate(-50%, -50%);\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    left: 40%;\n  }\n`;\n_c12 = BuildPlanetPosition;\nconst ScalePlanetPosition = styled.div`\n  position: absolute;\n  right: 18%;\n  top: 45%;\n  transform: translate(-50%, -50%);\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    right: 0;\n  }\n`;\n_c13 = ScalePlanetPosition;\nconst ConnectorLine = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(to right, \n    rgba(0, 210, 194, 0.7), \n    rgba(138, 112, 255, 0.7)\n  );\n  z-index: 0;\n  opacity: 0;\n  animation: ${slideIn} 1s forwards ease-out;\n  animation-delay: 0.5s;\n  \n  &::before, &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    width: 12px;\n    height: 12px;\n    border-radius: 50%;\n  }\n  \n  &::before {\n    left: 25%;\n    background-color: rgba(0, 210, 194, 1);\n    box-shadow: 0 0 10px rgba(0, 210, 194, 1);\n  }\n  \n  &::after {\n    right: 25%;\n    background-color: rgba(138, 112, 255, 1);\n    box-shadow: 0 0 10px rgba(138, 112, 255, 1);\n  }\n`;\n_c14 = ConnectorLine;\nconst CrossConnector = styled.div`\n  position: absolute;\n  width: 70%;\n  height: 70%;\n  left: 15%;\n  top: 15%;\n  border: 1px dashed rgba(138, 112, 255, 0.2);\n  border-radius: 50%;\n  z-index: 0;\n  transform: rotate(45deg);\n  opacity: 0;\n  animation: ${slideIn} 1s forwards ease-out;\n  animation-delay: 0.7s;\n`;\n_c15 = CrossConnector;\nconst FeatureCardStyled = styled(FeatureCard)`\n  opacity: 0;\n  animation: ${slideIn} 0.8s forwards ease-out;\n  animation-delay: ${props => `${0.3 + (props.delay || 0) * 0.1}s`};\n  transition: all 0.3s ease;\n  \n  ${props => props.active && `\n    opacity: 1;\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\n  `}\n  \n  &:hover {\n    transform: translateY(-7px) scale(1.02);\n  }\n`;\n_c16 = FeatureCardStyled;\nconst Features = () => {\n  _s();\n  const [activeFeature, setActiveFeature] = useState(null);\n  const [activeBuildIndex, setActiveBuildIndex] = useState(null);\n  const [activeScaleIndex, setActiveScaleIndex] = useState(null);\n  const planetsContainerRef = useRef(null);\n\n  // Parallax effect for the planets container\n  useEffect(() => {\n    const handleMouseMove = e => {\n      if (!planetsContainerRef.current) return;\n      const container = planetsContainerRef.current;\n      const {\n        left,\n        top,\n        width,\n        height\n      } = container.getBoundingClientRect();\n      const x = (e.clientX - left) / width - 0.5;\n      const y = (e.clientY - top) / height - 0.5;\n      container.style.transform = `rotateY(${x * 5}deg) rotateX(${-y * 5}deg)`;\n    };\n    const handleMouseLeave = () => {\n      if (!planetsContainerRef.current) return;\n      planetsContainerRef.current.style.transform = 'rotateY(0deg) rotateX(0deg)';\n    };\n    const container = planetsContainerRef.current;\n    if (container) {\n      container.addEventListener('mousemove', handleMouseMove);\n      container.addEventListener('mouseleave', handleMouseLeave);\n      return () => {\n        container.removeEventListener('mousemove', handleMouseMove);\n        container.removeEventListener('mouseleave', handleMouseLeave);\n      };\n    }\n  }, []);\n  const features = [{\n    id: 'virtual-testnets',\n    title: 'Virtual Testnets',\n    description: 'Replace rigid public testnets with customizable, zero-setup dev environments',\n    icon: /*#__PURE__*/_jsxDEV(TestnetIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 13\n    }, this),\n    category: 'build',\n    color: '#00d2c2'\n  }, {\n    id: 'smart-contract-dev',\n    title: 'Smart Contract Development',\n    description: 'Build and test smart contracts with a full development environment',\n    icon: /*#__PURE__*/_jsxDEV(DevelopmentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 13\n    }, this),\n    category: 'build',\n    color: '#00d2c2'\n  }, {\n    id: 'framework-integration',\n    title: 'Framework Integration',\n    description: 'Move across EVM chains with your entire stack natively supported',\n    icon: /*#__PURE__*/_jsxDEV(FrameworkIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 13\n    }, this),\n    category: 'build',\n    color: '#00d2c2'\n  }, {\n    id: 'testing-integration',\n    title: 'Testing & Integration',\n    description: 'Seamlessly integrate with your existing testing workflow',\n    icon: /*#__PURE__*/_jsxDEV(IntegrationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 393,\n      columnNumber: 13\n    }, this),\n    category: 'build',\n    color: '#00d2c2'\n  }, {\n    id: 'node-rpc',\n    title: 'Node RPC',\n    description: 'Go beyond the node standard with a scalable and extensible Node RPC',\n    icon: /*#__PURE__*/_jsxDEV(RPCIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 13\n    }, this),\n    category: 'scale',\n    color: '#8a70ff'\n  }, {\n    id: 'team-collaboration',\n    title: 'Team Collaboration',\n    description: 'Work together in real-time with your team on your blockchain projects',\n    icon: /*#__PURE__*/_jsxDEV(CollaborationIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 13\n    }, this),\n    category: 'scale',\n    color: '#8a70ff'\n  }, {\n    id: 'global-rpc',\n    title: 'Global RPC Traffic',\n    description: 'Advanced API & RPC services with global distribution for low latency',\n    icon: /*#__PURE__*/_jsxDEV(GlobalIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 417,\n      columnNumber: 13\n    }, this),\n    category: 'scale',\n    color: '#8a70ff'\n  }, {\n    id: 'chain-monitoring',\n    title: 'Chain Ops & Monitoring',\n    description: 'Access granular on-chain data with Web3-native dev tooling',\n    icon: /*#__PURE__*/_jsxDEV(MonitoringIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 13\n    }, this),\n    category: 'scale',\n    color: '#8a70ff'\n  }];\n  const buildFeatures = features.filter(feature => feature.category === 'build');\n  const scaleFeatures = features.filter(feature => feature.category === 'scale');\n  const activeFeatureData = features.find(feature => feature.id === activeFeature);\n  const buildIcons = buildFeatures.map(feature => feature.icon);\n  const scaleIcons = scaleFeatures.map(feature => feature.icon);\n  const handleFeatureClick = id => {\n    if (activeFeature === id) {\n      setActiveFeature(null);\n      setActiveBuildIndex(null);\n      setActiveScaleIndex(null);\n    } else {\n      setActiveFeature(id);\n      const feature = features.find(f => f.id === id);\n      if (feature) {\n        if (feature.category === 'build') {\n          const index = buildFeatures.findIndex(f => f.id === id);\n          setActiveBuildIndex(index);\n          setActiveScaleIndex(null);\n        } else {\n          const index = scaleFeatures.findIndex(f => f.id === id);\n          setActiveScaleIndex(index);\n          setActiveBuildIndex(null);\n        }\n      }\n    }\n  };\n  const handleBuildOrbitPointClick = index => {\n    var _buildFeatures$index;\n    const featureId = (_buildFeatures$index = buildFeatures[index]) === null || _buildFeatures$index === void 0 ? void 0 : _buildFeatures$index.id;\n    if (featureId) {\n      handleFeatureClick(featureId);\n    }\n  };\n  const handleScaleOrbitPointClick = index => {\n    var _scaleFeatures$index;\n    const featureId = (_scaleFeatures$index = scaleFeatures[index]) === null || _scaleFeatures$index === void 0 ? void 0 : _scaleFeatures$index.id;\n    if (featureId) {\n      handleFeatureClick(featureId);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(FeaturesSectionContainer, {\n    children: [/*#__PURE__*/_jsxDEV(BackgroundPattern, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 477,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentContainer, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          children: [\"Web3 development stack. \", /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Turbocharged.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 42\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Subtitle, {\n          children: \"Accelerate your on-chain velocity by adopting the most advanced, full-stack development platform for Web3.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 479,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeaturesContainer, {\n        children: [/*#__PURE__*/_jsxDEV(PlanetsContainer, {\n          ref: planetsContainerRef,\n          children: [/*#__PURE__*/_jsxDEV(ConnectorLine, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(CrossConnector, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PlanetsWrapper, {\n            children: [/*#__PURE__*/_jsxDEV(BuildPlanetPosition, {\n              children: /*#__PURE__*/_jsxDEV(Planet, {\n                type: \"build\",\n                size: \"280px\",\n                active: (activeFeatureData === null || activeFeatureData === void 0 ? void 0 : activeFeatureData.category) === 'build',\n                color: \"rgba(0, 210, 194, 0.6)\",\n                zIndex: 2,\n                orbitPoints: buildIcons,\n                activeFeatureIndex: activeBuildIndex !== null ? activeBuildIndex : undefined,\n                onFeatureClick: handleBuildOrbitPointClick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ScalePlanetPosition, {\n              children: /*#__PURE__*/_jsxDEV(Planet, {\n                type: \"scale\",\n                size: \"250px\",\n                active: (activeFeatureData === null || activeFeatureData === void 0 ? void 0 : activeFeatureData.category) === 'scale',\n                color: \"rgba(138, 112, 255, 0.6)\",\n                delay: \"2s\",\n                zIndex: 2,\n                orbitPoints: scaleIcons,\n                activeFeatureIndex: activeScaleIndex !== null ? activeScaleIndex : undefined,\n                onFeatureClick: handleScaleOrbitPointClick\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureBoxesGrid, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureBox, {\n            color: \"#00d2c2\",\n            position: \"topleft\",\n            children: [/*#__PURE__*/_jsxDEV(FeatureBoxTitle, {\n              color: \"#00d2c2\",\n              children: \"VIRTUAL TESTNETS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureItemsContainer, {\n              children: buildFeatures.slice(0, 2).map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCardStyled, {\n                title: feature.title,\n                description: feature.description,\n                icon: feature.icon,\n                color: feature.color,\n                active: activeFeature === feature.id,\n                onClick: () => handleFeatureClick(feature.id),\n                delay: index\n              }, feature.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureBox, {\n            color: \"#8a70ff\",\n            position: \"topright\",\n            children: [/*#__PURE__*/_jsxDEV(FeatureBoxTitle, {\n              color: \"#8a70ff\",\n              children: \"DEVELOPER EXPLORER\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureItemsContainer, {\n              children: scaleFeatures.slice(0, 2).map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCardStyled, {\n                title: feature.title,\n                description: feature.description,\n                icon: feature.icon,\n                color: feature.color,\n                active: activeFeature === feature.id,\n                onClick: () => handleFeatureClick(feature.id),\n                delay: index + 2\n              }, feature.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureBox, {\n            color: \"#00d2c2\",\n            position: \"bottomleft\",\n            children: [/*#__PURE__*/_jsxDEV(FeatureBoxTitle, {\n              color: \"#00d2c2\",\n              children: \"INTEGRATIONS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureItemsContainer, {\n              children: buildFeatures.slice(2, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCardStyled, {\n                title: feature.title,\n                description: feature.description,\n                icon: feature.icon,\n                color: feature.color,\n                active: activeFeature === feature.id,\n                onClick: () => handleFeatureClick(feature.id),\n                delay: index + 4\n              }, feature.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeatureBox, {\n            color: \"#8a70ff\",\n            position: \"bottomright\",\n            children: [/*#__PURE__*/_jsxDEV(FeatureBoxTitle, {\n              color: \"#8a70ff\",\n              children: \"NODE RPC\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(FeatureItemsContainer, {\n              children: scaleFeatures.slice(2, 4).map((feature, index) => /*#__PURE__*/_jsxDEV(FeatureCardStyled, {\n                title: feature.title,\n                description: feature.description,\n                icon: feature.icon,\n                color: feature.color,\n                active: activeFeature === feature.id,\n                onClick: () => handleFeatureClick(feature.id),\n                delay: index + 6\n              }, feature.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 478,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 476,\n    columnNumber: 5\n  }, this);\n};\n_s(Features, \"ugEkdh9qYNJG9EZSlNROLYlnkyo=\");\n_c17 = Features;\nexport default Features;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17;\n$RefreshReg$(_c, \"FeaturesSectionContainer\");\n$RefreshReg$(_c2, \"BackgroundPattern\");\n$RefreshReg$(_c3, \"ContentContainer\");\n$RefreshReg$(_c4, \"SectionHeader\");\n$RefreshReg$(_c5, \"Title\");\n$RefreshReg$(_c6, \"Subtitle\");\n$RefreshReg$(_c7, \"FeaturesContainer\");\n$RefreshReg$(_c8, \"PlanetsContainer\");\n$RefreshReg$(_c9, \"PlanetsWrapper\");\n$RefreshReg$(_c0, \"FeatureBoxesGrid\");\n$RefreshReg$(_c1, \"FeatureBox\");\n$RefreshReg$(_c10, \"FeatureBoxTitle\");\n$RefreshReg$(_c11, \"FeatureItemsContainer\");\n$RefreshReg$(_c12, \"BuildPlanetPosition\");\n$RefreshReg$(_c13, \"ScalePlanetPosition\");\n$RefreshReg$(_c14, \"ConnectorLine\");\n$RefreshReg$(_c15, \"CrossConnector\");\n$RefreshReg$(_c16, \"FeatureCardStyled\");\n$RefreshReg$(_c17, \"Features\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "styled", "keyframes", "Planet", "FeatureCard", "theme", "TestnetIcon", "IntegrationIcon", "DevelopmentIcon", "RPCIcon", "CollaborationIcon", "MonitoringIcon", "FrameworkIcon", "GlobalIcon", "jsxDEV", "_jsxDEV", "glow", "borderGlow", "slideIn", "lineGrow", "FeaturesSectionContainer", "section", "_c", "BackgroundPattern", "div", "_c2", "ContentContainer", "_c3", "SectionHeader", "_c4", "Title", "h2", "colors", "text", "secondary", "_c5", "Subtitle", "p", "textSecondary", "_c6", "FeaturesContainer", "_c7", "PlanetsContainer", "breakpoints", "md", "_c8", "PlanetsWrapper", "_c9", "FeatureBoxesGrid", "_c0", "FeatureBox", "props", "color", "position", "_c1", "FeatureBoxTitle", "h3", "_c10", "FeatureItemsContainer", "_c11", "BuildPlanetPosition", "_c12", "ScalePlanetPosition", "_c13", "ConnectorLine", "_c14", "CrossConnector", "_c15", "FeatureCardStyled", "delay", "active", "_c16", "Features", "_s", "activeFeature", "setActiveFeature", "activeBuildIndex", "setActiveBuildIndex", "activeScaleIndex", "setActiveScaleIndex", "planetsContainerRef", "handleMouseMove", "e", "current", "container", "left", "top", "width", "height", "getBoundingClientRect", "x", "clientX", "y", "clientY", "style", "transform", "handleMouseLeave", "addEventListener", "removeEventListener", "features", "id", "title", "description", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category", "buildFeatures", "filter", "feature", "scaleFeatures", "activeFeatureData", "find", "buildIcons", "map", "scaleIcons", "handleFeatureClick", "f", "index", "findIndex", "handleBuildOrbitPointClick", "_buildFeatures$index", "featureId", "handleScaleOrbitPointClick", "_scaleFeatures$index", "children", "ref", "type", "size", "zIndex", "orbitPoints", "activeFeatureIndex", "undefined", "onFeatureClick", "slice", "onClick", "_c17", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Features/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport Planet from './Planets/Planet';\nimport FeatureCard from './Cards/FeatureCard';\nimport { theme } from '../../styles/theme';\nimport {\n  TestnetIcon,\n  IntegrationIcon,\n  DevelopmentIcon,\n  RPCIcon,\n  CollaborationIcon,\n  MonitoringIcon,\n  FrameworkIcon,\n  GlobalIcon\n} from './Icons';\n\nconst glow = keyframes`\n  0%, 100% {\n    box-shadow: 0 0 5px rgba(138, 112, 255, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 15px rgba(138, 112, 255, 0.6);\n  }\n`;\n\nconst borderGlow = keyframes`\n  0%, 100% {\n    border-color: rgba(138, 112, 255, 0.3);\n  }\n  50% {\n    border-color: rgba(138, 112, 255, 0.6);\n  }\n`;\n\nconst slideIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n`;\n\nconst lineGrow = keyframes`\n  from {\n    width: 0;\n  }\n  to {\n    width: 100%;\n  }\n`;\n\nconst FeaturesSectionContainer = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  padding: 2rem 0 5rem;\n  position: relative;\n  overflow: hidden;\n`;\n\nconst BackgroundPattern = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(rgba(14, 14, 28, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(14, 14, 28, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  z-index: 1;\n  pointer-events: none;\n`;\n\nconst ContentContainer = styled.div`\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 0 1rem;\n  position: relative;\n  z-index: 2;\n`;\n\nconst SectionHeader = styled.div`\n  text-align: center;\n  margin-bottom: 2rem;\n  opacity: 0;\n  animation: ${slideIn} 0.8s forwards ease-out;\n`;\n\nconst Title = styled.h2`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1rem;\n  line-height: 1.2;\n  \n  span {\n    color: ${theme.colors.secondary};\n    display: inline-block;\n  }\n`;\n\nconst Subtitle = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  max-width: 700px;\n  margin: 0 auto;\n  line-height: 1.6;\n`;\n\nconst FeaturesContainer = styled.div`\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n`;\n\nconst PlanetsContainer = styled.div`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 800px;\n  perspective: 1000px;\n  margin-bottom: 0;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    height: 600px;\n  }\n`;\n\nconst PlanetsWrapper = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  transform-style: preserve-3d;\n`;\n\nconst FeatureBoxesGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-template-rows: repeat(2, auto);\n  gap: 1.5rem;\n  position: relative;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst FeatureBox = styled.div<{ color: string; position: 'topleft' | 'topright' | 'bottomleft' | 'bottomright' }>`\n  position: relative;\n  padding: 1.5rem;\n  background: rgba(14, 14, 28, 0.5);\n  backdrop-filter: blur(10px);\n  border: 1px solid ${props => props.color || 'rgba(138, 112, 255, 0.2)'};\n  border-radius: 8px;\n  animation: ${borderGlow} 4s infinite ease-in-out;\n  overflow: hidden;\n  \n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    background: linear-gradient(to bottom right, \n      ${props => props.color}05 0%, \n      transparent 40%, \n      transparent 60%, \n      ${props => props.color}05 100%\n    );\n    opacity: 0.3;\n    z-index: -1;\n  }\n  \n  // Define different corner styles\n  ${props => {\n    switch(props.position) {\n      case 'topleft':\n        return `\n          grid-column: 1;\n          grid-row: 1;\n        `;\n      case 'topright':\n        return `\n          grid-column: 2;\n          grid-row: 1;\n        `;\n      case 'bottomleft':\n        return `\n          grid-column: 1;\n          grid-row: 2;\n        `;\n      case 'bottomright':\n        return `\n          grid-column: 2;\n          grid-row: 2;\n        `;\n      default:\n        return '';\n    }\n  }}\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-column: 1;\n  }\n`;\n\nconst FeatureBoxTitle = styled.h3<{ color: string }>`\n  font-size: 1.5rem;\n  color: ${props => props.color};\n  margin-bottom: 1rem;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  position: relative;\n  display: inline-block;\n`;\n\nconst FeatureItemsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 1rem;\n`;\n\nconst BuildPlanetPosition = styled.div`\n  position: absolute;\n  left: 38%;\n  top: 45%;\n  transform: translate(-50%, -50%);\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    left: 40%;\n  }\n`;\n\nconst ScalePlanetPosition = styled.div`\n  position: absolute;\n  right: 18%;\n  top: 45%;\n  transform: translate(-50%, -50%);\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    right: 0;\n  }\n`;\n\nconst ConnectorLine = styled.div`\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: linear-gradient(to right, \n    rgba(0, 210, 194, 0.7), \n    rgba(138, 112, 255, 0.7)\n  );\n  z-index: 0;\n  opacity: 0;\n  animation: ${slideIn} 1s forwards ease-out;\n  animation-delay: 0.5s;\n  \n  &::before, &::after {\n    content: '';\n    position: absolute;\n    top: -5px;\n    width: 12px;\n    height: 12px;\n    border-radius: 50%;\n  }\n  \n  &::before {\n    left: 25%;\n    background-color: rgba(0, 210, 194, 1);\n    box-shadow: 0 0 10px rgba(0, 210, 194, 1);\n  }\n  \n  &::after {\n    right: 25%;\n    background-color: rgba(138, 112, 255, 1);\n    box-shadow: 0 0 10px rgba(138, 112, 255, 1);\n  }\n`;\n\nconst CrossConnector = styled.div`\n  position: absolute;\n  width: 70%;\n  height: 70%;\n  left: 15%;\n  top: 15%;\n  border: 1px dashed rgba(138, 112, 255, 0.2);\n  border-radius: 50%;\n  z-index: 0;\n  transform: rotate(45deg);\n  opacity: 0;\n  animation: ${slideIn} 1s forwards ease-out;\n  animation-delay: 0.7s;\n`;\n\nconst FeatureCardStyled = styled(FeatureCard)<{ active?: boolean; delay?: number }>`\n  opacity: 0;\n  animation: ${slideIn} 0.8s forwards ease-out;\n  animation-delay: ${props => `${0.3 + (props.delay || 0) * 0.1}s`};\n  transition: all 0.3s ease;\n  \n  ${props => props.active && `\n    opacity: 1;\n    transform: translateY(-5px);\n    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);\n  `}\n  \n  &:hover {\n    transform: translateY(-7px) scale(1.02);\n  }\n`;\n\ninterface FeatureData {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ReactNode;\n  category: 'build' | 'scale';\n  color: string;\n}\n\nconst Features: React.FC = () => {\n  const [activeFeature, setActiveFeature] = useState<string | null>(null);\n  const [activeBuildIndex, setActiveBuildIndex] = useState<number | null>(null);\n  const [activeScaleIndex, setActiveScaleIndex] = useState<number | null>(null);\n  const planetsContainerRef = useRef<HTMLDivElement>(null);\n  \n  // Parallax effect for the planets container\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      if (!planetsContainerRef.current) return;\n      \n      const container = planetsContainerRef.current;\n      const { left, top, width, height } = container.getBoundingClientRect();\n      const x = (e.clientX - left) / width - 0.5;\n      const y = (e.clientY - top) / height - 0.5;\n      \n      container.style.transform = `rotateY(${x * 5}deg) rotateX(${-y * 5}deg)`;\n    };\n    \n    const handleMouseLeave = () => {\n      if (!planetsContainerRef.current) return;\n      planetsContainerRef.current.style.transform = 'rotateY(0deg) rotateX(0deg)';\n    };\n    \n    const container = planetsContainerRef.current;\n    if (container) {\n      container.addEventListener('mousemove', handleMouseMove);\n      container.addEventListener('mouseleave', handleMouseLeave);\n      \n      return () => {\n        container.removeEventListener('mousemove', handleMouseMove);\n        container.removeEventListener('mouseleave', handleMouseLeave);\n      };\n    }\n  }, []);\n  \n  const features: FeatureData[] = [\n    {\n      id: 'virtual-testnets',\n      title: 'Virtual Testnets',\n      description: 'Replace rigid public testnets with customizable, zero-setup dev environments',\n      icon: <TestnetIcon />,\n      category: 'build',\n      color: '#00d2c2'\n    },\n    {\n      id: 'smart-contract-dev',\n      title: 'Smart Contract Development',\n      description: 'Build and test smart contracts with a full development environment',\n      icon: <DevelopmentIcon />,\n      category: 'build',\n      color: '#00d2c2'\n    },\n    {\n      id: 'framework-integration',\n      title: 'Framework Integration',\n      description: 'Move across EVM chains with your entire stack natively supported',\n      icon: <FrameworkIcon />,\n      category: 'build',\n      color: '#00d2c2'\n    },\n    {\n      id: 'testing-integration',\n      title: 'Testing & Integration',\n      description: 'Seamlessly integrate with your existing testing workflow',\n      icon: <IntegrationIcon />,\n      category: 'build',\n      color: '#00d2c2'\n    },\n    {\n      id: 'node-rpc',\n      title: 'Node RPC',\n      description: 'Go beyond the node standard with a scalable and extensible Node RPC',\n      icon: <RPCIcon />,\n      category: 'scale',\n      color: '#8a70ff'\n    },\n    {\n      id: 'team-collaboration',\n      title: 'Team Collaboration',\n      description: 'Work together in real-time with your team on your blockchain projects',\n      icon: <CollaborationIcon />,\n      category: 'scale',\n      color: '#8a70ff'\n    },\n    {\n      id: 'global-rpc',\n      title: 'Global RPC Traffic',\n      description: 'Advanced API & RPC services with global distribution for low latency',\n      icon: <GlobalIcon />,\n      category: 'scale',\n      color: '#8a70ff'\n    },\n    {\n      id: 'chain-monitoring',\n      title: 'Chain Ops & Monitoring',\n      description: 'Access granular on-chain data with Web3-native dev tooling',\n      icon: <MonitoringIcon />,\n      category: 'scale',\n      color: '#8a70ff'\n    }\n  ];\n  \n  const buildFeatures = features.filter(feature => feature.category === 'build');\n  const scaleFeatures = features.filter(feature => feature.category === 'scale');\n  \n  const activeFeatureData = features.find(feature => feature.id === activeFeature);\n  \n  const buildIcons = buildFeatures.map(feature => feature.icon);\n  const scaleIcons = scaleFeatures.map(feature => feature.icon);\n  \n  const handleFeatureClick = (id: string) => {\n    if (activeFeature === id) {\n      setActiveFeature(null);\n      setActiveBuildIndex(null);\n      setActiveScaleIndex(null);\n    } else {\n      setActiveFeature(id);\n      const feature = features.find(f => f.id === id);\n      if (feature) {\n        if (feature.category === 'build') {\n          const index = buildFeatures.findIndex(f => f.id === id);\n          setActiveBuildIndex(index);\n          setActiveScaleIndex(null);\n        } else {\n          const index = scaleFeatures.findIndex(f => f.id === id);\n          setActiveScaleIndex(index);\n          setActiveBuildIndex(null);\n        }\n      }\n    }\n  };\n  \n  const handleBuildOrbitPointClick = (index: number) => {\n    const featureId = buildFeatures[index]?.id;\n    if (featureId) {\n      handleFeatureClick(featureId);\n    }\n  };\n  \n  const handleScaleOrbitPointClick = (index: number) => {\n    const featureId = scaleFeatures[index]?.id;\n    if (featureId) {\n      handleFeatureClick(featureId);\n    }\n  };\n  \n  return (\n    <FeaturesSectionContainer>\n      <BackgroundPattern />\n      <ContentContainer>\n        <SectionHeader>\n          <Title>Web3 development stack. <span>Turbocharged.</span></Title>\n          <Subtitle>\n            Accelerate your on-chain velocity by adopting the most advanced, full-stack development platform for Web3.\n          </Subtitle>\n        </SectionHeader>\n        \n        <FeaturesContainer>\n          <PlanetsContainer ref={planetsContainerRef}>\n            <ConnectorLine />\n            <CrossConnector />\n            \n            <PlanetsWrapper>\n              <BuildPlanetPosition>\n                <Planet \n                  type=\"build\" \n                  size=\"280px\" \n                  active={activeFeatureData?.category === 'build'} \n                  color=\"rgba(0, 210, 194, 0.6)\"\n                  zIndex={2}\n                  orbitPoints={buildIcons}\n                  activeFeatureIndex={activeBuildIndex !== null ? activeBuildIndex : undefined}\n                  onFeatureClick={handleBuildOrbitPointClick}\n                />\n              </BuildPlanetPosition>\n              \n              <ScalePlanetPosition>\n                <Planet \n                  type=\"scale\" \n                  size=\"250px\"\n                  active={activeFeatureData?.category === 'scale'}\n                  color=\"rgba(138, 112, 255, 0.6)\"\n                  delay=\"2s\"\n                  zIndex={2}\n                  orbitPoints={scaleIcons}\n                  activeFeatureIndex={activeScaleIndex !== null ? activeScaleIndex : undefined}\n                  onFeatureClick={handleScaleOrbitPointClick}\n                />\n              </ScalePlanetPosition>\n            </PlanetsWrapper>\n          </PlanetsContainer>\n          \n          <FeatureBoxesGrid>\n            <FeatureBox color=\"#00d2c2\" position=\"topleft\">\n              <FeatureBoxTitle color=\"#00d2c2\">VIRTUAL TESTNETS</FeatureBoxTitle>\n              <FeatureItemsContainer>\n                {buildFeatures.slice(0, 2).map((feature, index) => (\n                  <FeatureCardStyled \n                    key={feature.id}\n                    title={feature.title}\n                    description={feature.description}\n                    icon={feature.icon}\n                    color={feature.color}\n                    active={activeFeature === feature.id}\n                    onClick={() => handleFeatureClick(feature.id)}\n                    delay={index}\n                  />\n                ))}\n              </FeatureItemsContainer>\n            </FeatureBox>\n            \n            <FeatureBox color=\"#8a70ff\" position=\"topright\">\n              <FeatureBoxTitle color=\"#8a70ff\">DEVELOPER EXPLORER</FeatureBoxTitle>\n              <FeatureItemsContainer>\n                {scaleFeatures.slice(0, 2).map((feature, index) => (\n                  <FeatureCardStyled \n                    key={feature.id}\n                    title={feature.title}\n                    description={feature.description}\n                    icon={feature.icon}\n                    color={feature.color}\n                    active={activeFeature === feature.id}\n                    onClick={() => handleFeatureClick(feature.id)}\n                    delay={index + 2}\n                  />\n                ))}\n              </FeatureItemsContainer>\n            </FeatureBox>\n            \n            <FeatureBox color=\"#00d2c2\" position=\"bottomleft\">\n              <FeatureBoxTitle color=\"#00d2c2\">INTEGRATIONS</FeatureBoxTitle>\n              <FeatureItemsContainer>\n                {buildFeatures.slice(2, 4).map((feature, index) => (\n                  <FeatureCardStyled \n                    key={feature.id}\n                    title={feature.title}\n                    description={feature.description}\n                    icon={feature.icon}\n                    color={feature.color}\n                    active={activeFeature === feature.id}\n                    onClick={() => handleFeatureClick(feature.id)}\n                    delay={index + 4}\n                  />\n                ))}\n              </FeatureItemsContainer>\n            </FeatureBox>\n            \n            <FeatureBox color=\"#8a70ff\" position=\"bottomright\">\n              <FeatureBoxTitle color=\"#8a70ff\">NODE RPC</FeatureBoxTitle>\n              <FeatureItemsContainer>\n                {scaleFeatures.slice(2, 4).map((feature, index) => (\n                  <FeatureCardStyled \n                    key={feature.id}\n                    title={feature.title}\n                    description={feature.description}\n                    icon={feature.icon}\n                    color={feature.color}\n                    active={activeFeature === feature.id}\n                    onClick={() => handleFeatureClick(feature.id)}\n                    delay={index + 6}\n                  />\n                ))}\n              </FeatureItemsContainer>\n            </FeatureBox>\n          </FeatureBoxesGrid>\n        </FeaturesContainer>\n      </ContentContainer>\n    </FeaturesSectionContainer>\n  );\n};\n\nexport default Features; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,SACEC,WAAW,EACXC,eAAe,EACfC,eAAe,EACfC,OAAO,EACPC,iBAAiB,EACjBC,cAAc,EACdC,aAAa,EACbC,UAAU,QACL,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjB,MAAMC,IAAI,GAAGd,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMe,UAAU,GAAGf,SAAS;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMgB,OAAO,GAAGhB,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMiB,QAAQ,GAAGjB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMkB,wBAAwB,GAAGnB,MAAM,CAACoB,OAAO;AAC/C;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GANIF,wBAAwB;AAQ9B,MAAMG,iBAAiB,GAAGtB,MAAM,CAACuB,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,iBAAiB;AAavB,MAAMG,gBAAgB,GAAGzB,MAAM,CAACuB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GANID,gBAAgB;AAQtB,MAAME,aAAa,GAAG3B,MAAM,CAACuB,GAAG;AAChC;AACA;AACA;AACA,eAAeN,OAAO;AACtB,CAAC;AAACW,GAAA,GALID,aAAa;AAOnB,MAAME,KAAK,GAAG7B,MAAM,CAAC8B,EAAE;AACvB;AACA;AACA,WAAW1B,KAAK,CAAC2B,MAAM,CAACC,IAAI;AAC5B;AACA;AACA;AACA;AACA,aAAa5B,KAAK,CAAC2B,MAAM,CAACE,SAAS;AACnC;AACA;AACA,CAAC;AAACC,GAAA,GAXIL,KAAK;AAaX,MAAMM,QAAQ,GAAGnC,MAAM,CAACoC,CAAC;AACzB;AACA,WAAWhC,KAAK,CAAC2B,MAAM,CAACM,aAAa;AACrC;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIH,QAAQ;AAQd,MAAMI,iBAAiB,GAAGvC,MAAM,CAACuB,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GALID,iBAAiB;AAOvB,MAAME,gBAAgB,GAAGzC,MAAM,CAACuB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACsC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,gBAAgB;AActB,MAAMI,cAAc,GAAG7C,MAAM,CAACuB,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GALID,cAAc;AAOpB,MAAME,gBAAgB,GAAG/C,MAAM,CAACuB,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACsC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACK,GAAA,GAVID,gBAAgB;AAYtB,MAAME,UAAU,GAAGjD,MAAM,CAACuB,GAAuF;AACjH;AACA;AACA;AACA;AACA,sBAAsB2B,KAAK,IAAIA,KAAK,CAACC,KAAK,IAAI,0BAA0B;AACxE;AACA,eAAenC,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQkC,KAAK,IAAIA,KAAK,CAACC,KAAK;AAC5B;AACA;AACA,QAAQD,KAAK,IAAIA,KAAK,CAACC,KAAK;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,IAAID,KAAK,IAAI;EACT,QAAOA,KAAK,CAACE,QAAQ;IACnB,KAAK,SAAS;MACZ,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,UAAU;MACb,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,YAAY;MACf,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,aAAa;MAChB,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO,EAAE;EACb;AACF,CAAC;AACH;AACA,uBAAuBhD,KAAK,CAACsC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACU,GAAA,GA1DIJ,UAAU;AA4DhB,MAAMK,eAAe,GAAGtD,MAAM,CAACuD,EAAqB;AACpD;AACA,WAAWL,KAAK,IAAIA,KAAK,CAACC,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,IAAA,GARIF,eAAe;AAUrB,MAAMG,qBAAqB,GAAGzD,MAAM,CAACuB,GAAG;AACxC;AACA;AACA;AACA,CAAC;AAACmC,IAAA,GAJID,qBAAqB;AAM3B,MAAME,mBAAmB,GAAG3D,MAAM,CAACuB,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACsC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACiB,IAAA,GATID,mBAAmB;AAWzB,MAAME,mBAAmB,GAAG7D,MAAM,CAACuB,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA,uBAAuBnB,KAAK,CAACsC,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACmB,IAAA,GATID,mBAAmB;AAWzB,MAAME,aAAa,GAAG/D,MAAM,CAACuB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeN,OAAO;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+C,IAAA,GAnCID,aAAa;AAqCnB,MAAME,cAAc,GAAGjE,MAAM,CAACuB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeN,OAAO;AACtB;AACA,CAAC;AAACiD,IAAA,GAbID,cAAc;AAepB,MAAME,iBAAiB,GAAGnE,MAAM,CAACG,WAAW,CAAuC;AACnF;AACA,eAAec,OAAO;AACtB,qBAAqBiC,KAAK,IAAI,GAAG,GAAG,GAAG,CAACA,KAAK,CAACkB,KAAK,IAAI,CAAC,IAAI,GAAG,GAAG;AAClE;AACA;AACA,IAAIlB,KAAK,IAAIA,KAAK,CAACmB,MAAM,IAAI;AAC7B;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAfIH,iBAAiB;AA0BvB,MAAMI,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAgB,IAAI,CAAC;EACvE,MAAM,CAAC8E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/E,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAM,CAACgF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjF,QAAQ,CAAgB,IAAI,CAAC;EAC7E,MAAMkF,mBAAmB,GAAGhF,MAAM,CAAiB,IAAI,CAAC;;EAExD;EACAD,SAAS,CAAC,MAAM;IACd,MAAMkF,eAAe,GAAIC,CAAa,IAAK;MACzC,IAAI,CAACF,mBAAmB,CAACG,OAAO,EAAE;MAElC,MAAMC,SAAS,GAAGJ,mBAAmB,CAACG,OAAO;MAC7C,MAAM;QAAEE,IAAI;QAAEC,GAAG;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAGJ,SAAS,CAACK,qBAAqB,CAAC,CAAC;MACtE,MAAMC,CAAC,GAAG,CAACR,CAAC,CAACS,OAAO,GAAGN,IAAI,IAAIE,KAAK,GAAG,GAAG;MAC1C,MAAMK,CAAC,GAAG,CAACV,CAAC,CAACW,OAAO,GAAGP,GAAG,IAAIE,MAAM,GAAG,GAAG;MAE1CJ,SAAS,CAACU,KAAK,CAACC,SAAS,GAAG,WAAWL,CAAC,GAAG,CAAC,gBAAgB,CAACE,CAAC,GAAG,CAAC,MAAM;IAC1E,CAAC;IAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,IAAI,CAAChB,mBAAmB,CAACG,OAAO,EAAE;MAClCH,mBAAmB,CAACG,OAAO,CAACW,KAAK,CAACC,SAAS,GAAG,6BAA6B;IAC7E,CAAC;IAED,MAAMX,SAAS,GAAGJ,mBAAmB,CAACG,OAAO;IAC7C,IAAIC,SAAS,EAAE;MACbA,SAAS,CAACa,gBAAgB,CAAC,WAAW,EAAEhB,eAAe,CAAC;MACxDG,SAAS,CAACa,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,CAAC;MAE1D,OAAO,MAAM;QACXZ,SAAS,CAACc,mBAAmB,CAAC,WAAW,EAAEjB,eAAe,CAAC;QAC3DG,SAAS,CAACc,mBAAmB,CAAC,YAAY,EAAEF,gBAAgB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,QAAuB,GAAG,CAC9B;IACEC,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,8EAA8E;IAC3FC,IAAI,eAAExF,OAAA,CAACT,WAAW;MAAAkG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,oEAAoE;IACjFC,IAAI,eAAExF,OAAA,CAACP,eAAe;MAAAgG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,uBAAuB;IAC3BC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,kEAAkE;IAC/EC,IAAI,eAAExF,OAAA,CAACH,aAAa;MAAA4F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,qBAAqB;IACzBC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,0DAA0D;IACvEC,IAAI,eAAExF,OAAA,CAACR,eAAe;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,qEAAqE;IAClFC,IAAI,eAAExF,OAAA,CAACN,OAAO;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,oBAAoB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,uEAAuE;IACpFC,IAAI,eAAExF,OAAA,CAACL,iBAAiB;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,YAAY;IAChBC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,sEAAsE;IACnFC,IAAI,eAAExF,OAAA,CAACF,UAAU;MAAA2F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,EACD;IACEgD,EAAE,EAAE,kBAAkB;IACtBC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,4DAA4D;IACzEC,IAAI,eAAExF,OAAA,CAACJ,cAAc;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,QAAQ,EAAE,OAAO;IACjBxD,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMyD,aAAa,GAAGV,QAAQ,CAACW,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACH,QAAQ,KAAK,OAAO,CAAC;EAC9E,MAAMI,aAAa,GAAGb,QAAQ,CAACW,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACH,QAAQ,KAAK,OAAO,CAAC;EAE9E,MAAMK,iBAAiB,GAAGd,QAAQ,CAACe,IAAI,CAACH,OAAO,IAAIA,OAAO,CAACX,EAAE,KAAK1B,aAAa,CAAC;EAEhF,MAAMyC,UAAU,GAAGN,aAAa,CAACO,GAAG,CAACL,OAAO,IAAIA,OAAO,CAACR,IAAI,CAAC;EAC7D,MAAMc,UAAU,GAAGL,aAAa,CAACI,GAAG,CAACL,OAAO,IAAIA,OAAO,CAACR,IAAI,CAAC;EAE7D,MAAMe,kBAAkB,GAAIlB,EAAU,IAAK;IACzC,IAAI1B,aAAa,KAAK0B,EAAE,EAAE;MACxBzB,gBAAgB,CAAC,IAAI,CAAC;MACtBE,mBAAmB,CAAC,IAAI,CAAC;MACzBE,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLJ,gBAAgB,CAACyB,EAAE,CAAC;MACpB,MAAMW,OAAO,GAAGZ,QAAQ,CAACe,IAAI,CAACK,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKA,EAAE,CAAC;MAC/C,IAAIW,OAAO,EAAE;QACX,IAAIA,OAAO,CAACH,QAAQ,KAAK,OAAO,EAAE;UAChC,MAAMY,KAAK,GAAGX,aAAa,CAACY,SAAS,CAACF,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKA,EAAE,CAAC;UACvDvB,mBAAmB,CAAC2C,KAAK,CAAC;UAC1BzC,mBAAmB,CAAC,IAAI,CAAC;QAC3B,CAAC,MAAM;UACL,MAAMyC,KAAK,GAAGR,aAAa,CAACS,SAAS,CAACF,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKA,EAAE,CAAC;UACvDrB,mBAAmB,CAACyC,KAAK,CAAC;UAC1B3C,mBAAmB,CAAC,IAAI,CAAC;QAC3B;MACF;IACF;EACF,CAAC;EAED,MAAM6C,0BAA0B,GAAIF,KAAa,IAAK;IAAA,IAAAG,oBAAA;IACpD,MAAMC,SAAS,IAAAD,oBAAA,GAAGd,aAAa,CAACW,KAAK,CAAC,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBvB,EAAE;IAC1C,IAAIwB,SAAS,EAAE;MACbN,kBAAkB,CAACM,SAAS,CAAC;IAC/B;EACF,CAAC;EAED,MAAMC,0BAA0B,GAAIL,KAAa,IAAK;IAAA,IAAAM,oBAAA;IACpD,MAAMF,SAAS,IAAAE,oBAAA,GAAGd,aAAa,CAACQ,KAAK,CAAC,cAAAM,oBAAA,uBAApBA,oBAAA,CAAsB1B,EAAE;IAC1C,IAAIwB,SAAS,EAAE;MACbN,kBAAkB,CAACM,SAAS,CAAC;IAC/B;EACF,CAAC;EAED,oBACE7G,OAAA,CAACK,wBAAwB;IAAA2G,QAAA,gBACvBhH,OAAA,CAACQ,iBAAiB;MAAAiF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrB5F,OAAA,CAACW,gBAAgB;MAAAqG,QAAA,gBACfhH,OAAA,CAACa,aAAa;QAAAmG,QAAA,gBACZhH,OAAA,CAACe,KAAK;UAAAiG,QAAA,GAAC,0BAAwB,eAAAhH,OAAA;YAAAgH,QAAA,EAAM;UAAa;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE5F,OAAA,CAACqB,QAAQ;UAAA2F,QAAA,EAAC;QAEV;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEhB5F,OAAA,CAACyB,iBAAiB;QAAAuF,QAAA,gBAChBhH,OAAA,CAAC2B,gBAAgB;UAACsF,GAAG,EAAEhD,mBAAoB;UAAA+C,QAAA,gBACzChH,OAAA,CAACiD,aAAa;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjB5F,OAAA,CAACmD,cAAc;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAElB5F,OAAA,CAAC+B,cAAc;YAAAiF,QAAA,gBACbhH,OAAA,CAAC6C,mBAAmB;cAAAmE,QAAA,eAClBhH,OAAA,CAACZ,MAAM;gBACL8H,IAAI,EAAC,OAAO;gBACZC,IAAI,EAAC,OAAO;gBACZ5D,MAAM,EAAE,CAAA2C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEL,QAAQ,MAAK,OAAQ;gBAChDxD,KAAK,EAAC,wBAAwB;gBAC9B+E,MAAM,EAAE,CAAE;gBACVC,WAAW,EAAEjB,UAAW;gBACxBkB,kBAAkB,EAAEzD,gBAAgB,KAAK,IAAI,GAAGA,gBAAgB,GAAG0D,SAAU;gBAC7EC,cAAc,EAAEb;cAA2B;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACiB,CAAC,eAEtB5F,OAAA,CAAC+C,mBAAmB;cAAAiE,QAAA,eAClBhH,OAAA,CAACZ,MAAM;gBACL8H,IAAI,EAAC,OAAO;gBACZC,IAAI,EAAC,OAAO;gBACZ5D,MAAM,EAAE,CAAA2C,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEL,QAAQ,MAAK,OAAQ;gBAChDxD,KAAK,EAAC,0BAA0B;gBAChCiB,KAAK,EAAC,IAAI;gBACV8D,MAAM,EAAE,CAAE;gBACVC,WAAW,EAAEf,UAAW;gBACxBgB,kBAAkB,EAAEvD,gBAAgB,KAAK,IAAI,GAAGA,gBAAgB,GAAGwD,SAAU;gBAC7EC,cAAc,EAAEV;cAA2B;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACiB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEnB5F,OAAA,CAACiC,gBAAgB;UAAA+E,QAAA,gBACfhH,OAAA,CAACmC,UAAU;YAACE,KAAK,EAAC,SAAS;YAACC,QAAQ,EAAC,SAAS;YAAA0E,QAAA,gBAC5ChH,OAAA,CAACwC,eAAe;cAACH,KAAK,EAAC,SAAS;cAAA2E,QAAA,EAAC;YAAgB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eACnE5F,OAAA,CAAC2C,qBAAqB;cAAAqE,QAAA,EACnBlB,aAAa,CAAC2B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAACL,OAAO,EAAES,KAAK,kBAC5CzG,OAAA,CAACqD,iBAAiB;gBAEhBiC,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBC,WAAW,EAAES,OAAO,CAACT,WAAY;gBACjCC,IAAI,EAAEQ,OAAO,CAACR,IAAK;gBACnBnD,KAAK,EAAE2D,OAAO,CAAC3D,KAAM;gBACrBkB,MAAM,EAAEI,aAAa,KAAKqC,OAAO,CAACX,EAAG;gBACrCqC,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,OAAO,CAACX,EAAE,CAAE;gBAC9C/B,KAAK,EAAEmD;cAAM,GAPRT,OAAO,CAACX,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQhB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEb5F,OAAA,CAACmC,UAAU;YAACE,KAAK,EAAC,SAAS;YAACC,QAAQ,EAAC,UAAU;YAAA0E,QAAA,gBAC7ChH,OAAA,CAACwC,eAAe;cAACH,KAAK,EAAC,SAAS;cAAA2E,QAAA,EAAC;YAAkB;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eACrE5F,OAAA,CAAC2C,qBAAqB;cAAAqE,QAAA,EACnBf,aAAa,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAACL,OAAO,EAAES,KAAK,kBAC5CzG,OAAA,CAACqD,iBAAiB;gBAEhBiC,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBC,WAAW,EAAES,OAAO,CAACT,WAAY;gBACjCC,IAAI,EAAEQ,OAAO,CAACR,IAAK;gBACnBnD,KAAK,EAAE2D,OAAO,CAAC3D,KAAM;gBACrBkB,MAAM,EAAEI,aAAa,KAAKqC,OAAO,CAACX,EAAG;gBACrCqC,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,OAAO,CAACX,EAAE,CAAE;gBAC9C/B,KAAK,EAAEmD,KAAK,GAAG;cAAE,GAPZT,OAAO,CAACX,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQhB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEb5F,OAAA,CAACmC,UAAU;YAACE,KAAK,EAAC,SAAS;YAACC,QAAQ,EAAC,YAAY;YAAA0E,QAAA,gBAC/ChH,OAAA,CAACwC,eAAe;cAACH,KAAK,EAAC,SAAS;cAAA2E,QAAA,EAAC;YAAY;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC/D5F,OAAA,CAAC2C,qBAAqB;cAAAqE,QAAA,EACnBlB,aAAa,CAAC2B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAACL,OAAO,EAAES,KAAK,kBAC5CzG,OAAA,CAACqD,iBAAiB;gBAEhBiC,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBC,WAAW,EAAES,OAAO,CAACT,WAAY;gBACjCC,IAAI,EAAEQ,OAAO,CAACR,IAAK;gBACnBnD,KAAK,EAAE2D,OAAO,CAAC3D,KAAM;gBACrBkB,MAAM,EAAEI,aAAa,KAAKqC,OAAO,CAACX,EAAG;gBACrCqC,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,OAAO,CAACX,EAAE,CAAE;gBAC9C/B,KAAK,EAAEmD,KAAK,GAAG;cAAE,GAPZT,OAAO,CAACX,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQhB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAEb5F,OAAA,CAACmC,UAAU;YAACE,KAAK,EAAC,SAAS;YAACC,QAAQ,EAAC,aAAa;YAAA0E,QAAA,gBAChDhH,OAAA,CAACwC,eAAe;cAACH,KAAK,EAAC,SAAS;cAAA2E,QAAA,EAAC;YAAQ;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiB,CAAC,eAC3D5F,OAAA,CAAC2C,qBAAqB;cAAAqE,QAAA,EACnBf,aAAa,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACpB,GAAG,CAAC,CAACL,OAAO,EAAES,KAAK,kBAC5CzG,OAAA,CAACqD,iBAAiB;gBAEhBiC,KAAK,EAAEU,OAAO,CAACV,KAAM;gBACrBC,WAAW,EAAES,OAAO,CAACT,WAAY;gBACjCC,IAAI,EAAEQ,OAAO,CAACR,IAAK;gBACnBnD,KAAK,EAAE2D,OAAO,CAAC3D,KAAM;gBACrBkB,MAAM,EAAEI,aAAa,KAAKqC,OAAO,CAACX,EAAG;gBACrCqC,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACP,OAAO,CAACX,EAAE,CAAE;gBAC9C/B,KAAK,EAAEmD,KAAK,GAAG;cAAE,GAPZT,OAAO,CAACX,EAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQhB,CACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAE/B,CAAC;AAAClC,EAAA,CA9QID,QAAkB;AAAAkE,IAAA,GAAlBlE,QAAkB;AAgRxB,eAAeA,QAAQ;AAAC,IAAAlD,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAM,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAK,GAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAI,IAAA,EAAAmE,IAAA;AAAAC,YAAA,CAAArH,EAAA;AAAAqH,YAAA,CAAAlH,GAAA;AAAAkH,YAAA,CAAAhH,GAAA;AAAAgH,YAAA,CAAA9G,GAAA;AAAA8G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAApG,GAAA;AAAAoG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAArF,GAAA;AAAAqF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAhF,IAAA;AAAAgF,YAAA,CAAA9E,IAAA;AAAA8E,YAAA,CAAA5E,IAAA;AAAA4E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}