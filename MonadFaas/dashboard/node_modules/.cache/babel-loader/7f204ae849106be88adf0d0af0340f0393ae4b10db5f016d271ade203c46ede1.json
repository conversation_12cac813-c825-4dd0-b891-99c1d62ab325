{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/index.tsx\";\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport NavBar from './Nav';\nimport HeroContent from './Content';\nimport DashboardModel from './DashboardModel';\nimport { theme } from '../../styles/theme';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst floatAnimation = keyframes`\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n`;\nconst HeroSection = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n`;\n_c = HeroSection;\nconst FloatingOrb = styled.div`\n  position: absolute;\n  width: 300px;\n  height: 300px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, ${theme.colors.primary}33, ${theme.colors.secondary}33);\n  filter: blur(80px);\n  z-index: 0;\n  opacity: 0.5;\n  animation: ${floatAnimation} 10s ease-in-out infinite;\n  \n  &.orb1 {\n    top: -100px;\n    right: 10%;\n    animation-delay: 0s;\n  }\n  \n  &.orb2 {\n    bottom: -150px;\n    left: 5%;\n    width: 250px;\n    height: 250px;\n    animation-delay: -3s;\n  }\n`;\n_c2 = FloatingOrb;\nconst ContentWrapper = styled.div`\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n`;\n_c3 = ContentWrapper;\nconst MainContentContainer = styled.div`\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 5%;\n  flex: 1;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    flex-direction: column;\n    padding: 0 2rem;\n  }\n`;\n_c4 = MainContentContainer;\nconst LeftContent = styled.div`\n  flex: 1;\n  max-width: 600px;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 100%;\n    margin-bottom: 2rem;\n  }\n`;\n_c5 = LeftContent;\nconst RightContent = styled.div`\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    width: 100%;\n  }\n`;\n_c6 = RightContent;\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(HeroSection, {\n    children: [/*#__PURE__*/_jsxDEV(FloatingOrb, {\n      className: \"orb1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FloatingOrb, {\n      className: \"orb2\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ContentWrapper, {\n      children: [/*#__PURE__*/_jsxDEV(NavBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MainContentContainer, {\n        children: [/*#__PURE__*/_jsxDEV(LeftContent, {\n          children: /*#__PURE__*/_jsxDEV(HeroContent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RightContent, {\n          children: /*#__PURE__*/_jsxDEV(DashboardModel, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 103,\n    columnNumber: 5\n  }, this);\n};\n_c7 = Hero;\nexport default Hero;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"HeroSection\");\n$RefreshReg$(_c2, \"FloatingOrb\");\n$RefreshReg$(_c3, \"ContentWrapper\");\n$RefreshReg$(_c4, \"MainContentContainer\");\n$RefreshReg$(_c5, \"LeftContent\");\n$RefreshReg$(_c6, \"RightContent\");\n$RefreshReg$(_c7, \"Hero\");", "map": {"version": 3, "names": ["React", "styled", "keyframes", "NavBar", "Hero<PERSON><PERSON><PERSON>", "DashboardModel", "theme", "jsxDEV", "_jsxDEV", "floatAnimation", "HeroSection", "section", "_c", "FloatingOrb", "div", "colors", "primary", "secondary", "_c2", "ContentWrapper", "_c3", "MainContentContainer", "breakpoints", "lg", "_c4", "LeftContent", "_c5", "RightContent", "_c6", "Hero", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c7", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/index.tsx"], "sourcesContent": ["import React from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport NavBar from './Nav';\nimport HeroContent from './Content';\nimport DashboardModel from './DashboardModel';\nimport { theme } from '../../styles/theme';\n\nconst floatAnimation = keyframes`\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n`;\n\nconst HeroSection = styled.section`\n  width: 100%;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  padding: 2rem 0;\n  position: relative;\n  z-index: 1;\n`;\n\nconst FloatingOrb = styled.div`\n  position: absolute;\n  width: 300px;\n  height: 300px;\n  border-radius: 50%;\n  background: linear-gradient(45deg, ${theme.colors.primary}33, ${theme.colors.secondary}33);\n  filter: blur(80px);\n  z-index: 0;\n  opacity: 0.5;\n  animation: ${floatAnimation} 10s ease-in-out infinite;\n  \n  &.orb1 {\n    top: -100px;\n    right: 10%;\n    animation-delay: 0s;\n  }\n  \n  &.orb2 {\n    bottom: -150px;\n    left: 5%;\n    width: 250px;\n    height: 250px;\n    animation-delay: -3s;\n  }\n`;\n\nconst ContentWrapper = styled.div`\n  position: relative;\n  z-index: 2;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n`;\n\nconst MainContentContainer = styled.div`\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 5%;\n  flex: 1;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    flex-direction: column;\n    padding: 0 2rem;\n  }\n`;\n\nconst LeftContent = styled.div`\n  flex: 1;\n  max-width: 600px;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    max-width: 100%;\n    margin-bottom: 2rem;\n  }\n`;\n\nconst RightContent = styled.div`\n  flex: 1;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  \n  @media (max-width: ${theme.breakpoints.lg}) {\n    width: 100%;\n  }\n`;\n\nconst Hero: React.FC = () => {\n  return (\n    <HeroSection>\n      <FloatingOrb className=\"orb1\" />\n      <FloatingOrb className=\"orb2\" />\n      <ContentWrapper>\n        <NavBar />\n        <MainContentContainer>\n          <LeftContent>\n            <HeroContent />\n          </LeftContent>\n          <RightContent>\n            <DashboardModel />\n          </RightContent>\n        </MainContentContainer>\n      </ContentWrapper>\n    </HeroSection>\n  );\n};\n\nexport default Hero; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AACrD,OAAOC,MAAM,MAAM,OAAO;AAC1B,OAAOC,WAAW,MAAM,WAAW;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,KAAK,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGP,SAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMQ,WAAW,GAAGT,MAAM,CAACU,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,WAAW;AAUjB,MAAMG,WAAW,GAAGZ,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA,uCAAuCR,KAAK,CAACS,MAAM,CAACC,OAAO,OAAOV,KAAK,CAACS,MAAM,CAACE,SAAS;AACxF;AACA;AACA;AACA,eAAeR,cAAc;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAxBIL,WAAW;AA0BjB,MAAMM,cAAc,GAAGlB,MAAM,CAACa,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACM,GAAA,GARID,cAAc;AAUpB,MAAME,oBAAoB,GAAGpB,MAAM,CAACa,GAAG;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBR,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIH,oBAAoB;AAc1B,MAAMI,WAAW,GAAGxB,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA,uBAAuBR,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACG,GAAA,GARID,WAAW;AAUjB,MAAME,YAAY,GAAG1B,MAAM,CAACa,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,uBAAuBR,KAAK,CAACgB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACK,GAAA,GATID,YAAY;AAWlB,MAAME,IAAc,GAAGA,CAAA,KAAM;EAC3B,oBACErB,OAAA,CAACE,WAAW;IAAAoB,QAAA,gBACVtB,OAAA,CAACK,WAAW;MAACkB,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChC3B,OAAA,CAACK,WAAW;MAACkB,SAAS,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChC3B,OAAA,CAACW,cAAc;MAAAW,QAAA,gBACbtB,OAAA,CAACL,MAAM;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACV3B,OAAA,CAACa,oBAAoB;QAAAS,QAAA,gBACnBtB,OAAA,CAACiB,WAAW;UAAAK,QAAA,eACVtB,OAAA,CAACJ,WAAW;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACd3B,OAAA,CAACmB,YAAY;UAAAG,QAAA,eACXtB,OAAA,CAACH,cAAc;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAElB,CAAC;AAACC,GAAA,GAlBIP,IAAc;AAoBpB,eAAeA,IAAI;AAAC,IAAAjB,EAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAjB,GAAA;AAAAiB,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}