{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport Hero from './components/Hero';\nimport Features from './components/Features';\nimport Benefits from './components/Benefits';\nimport Explorer from './components/Explorer';\nimport GlobalStyles from './GlobalStyles';\nimport styled from 'styled-components';\nimport BackgroundImage from './components/Features/BackgroundImage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppContainer = styled.div`\n  position: relative;\n  width: 100%;\n  min-height: 100vh;\n  scroll-behavior: smooth;\n`;\n_c = AppContainer;\nconst BackgroundPattern = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(rgba(14, 14, 28, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(14, 14, 28, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  z-index: 1;\n  pointer-events: none;\n`;\n_c2 = BackgroundPattern;\nfunction App() {\n  _s();\n  // Apply scroll-snap behavior to body when Explorer section is in view\n  useEffect(() => {\n    const body = document.body;\n\n    // Add CSS for smooth scrolling\n    body.style.scrollBehavior = 'smooth';\n    return () => {\n      // Cleanup\n      body.style.scrollBehavior = '';\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(AppContainer, {\n    children: [/*#__PURE__*/_jsxDEV(GlobalStyles, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BackgroundImage, {\n      src: \"/features-bg.webp\",\n      overlayOpacity: 0.9\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BackgroundPattern, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Features, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Benefits, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Explorer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AppContainer\");\n$RefreshReg$(_c2, \"BackgroundPattern\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "Hero", "Features", "Benefits", "Explorer", "GlobalStyles", "styled", "BackgroundImage", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c", "BackgroundPattern", "_c2", "App", "_s", "body", "document", "style", "scroll<PERSON>eh<PERSON>or", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "overlayOpacity", "_c3", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport Hero from './components/Hero';\nimport Features from './components/Features';\nimport Benefits from './components/Benefits';\nimport Explorer from './components/Explorer';\nimport GlobalStyles from './GlobalStyles';\nimport styled from 'styled-components';\nimport BackgroundImage from './components/Features/BackgroundImage';\n\nconst AppContainer = styled.div`\n  position: relative;\n  width: 100%;\n  min-height: 100vh;\n  scroll-behavior: smooth;\n`;\n\nconst BackgroundPattern = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: linear-gradient(rgba(14, 14, 28, 0.1) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(14, 14, 28, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  z-index: 1;\n  pointer-events: none;\n`;\n\nfunction App() {\n  // Apply scroll-snap behavior to body when Explorer section is in view\n  useEffect(() => {\n    const body = document.body;\n    \n    // Add CSS for smooth scrolling\n    body.style.scrollBehavior = 'smooth';\n    \n    return () => {\n      // Cleanup\n      body.style.scrollBehavior = '';\n    };\n  }, []);\n  \n  return (\n    <AppContainer>\n      <GlobalStyles />\n      <BackgroundImage src=\"/features-bg.webp\" overlayOpacity={0.9} />\n      <BackgroundPattern />\n      <Hero />\n      <Features />\n      \n      <Benefits />\n      <Explorer />\n    </AppContainer>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,eAAe,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpE,MAAMC,YAAY,GAAGJ,MAAM,CAACK,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,YAAY;AAOlB,MAAMG,iBAAiB,GAAGP,MAAM,CAACK,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAXID,iBAAiB;AAavB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb;EACAhB,SAAS,CAAC,MAAM;IACd,MAAMiB,IAAI,GAAGC,QAAQ,CAACD,IAAI;;IAE1B;IACAA,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,QAAQ;IAEpC,OAAO,MAAM;MACX;MACAH,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,EAAE;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEX,OAAA,CAACC,YAAY;IAAAW,QAAA,gBACXZ,OAAA,CAACJ,YAAY;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChBhB,OAAA,CAACF,eAAe;MAACmB,GAAG,EAAC,mBAAmB;MAACC,cAAc,EAAE;IAAI;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChEhB,OAAA,CAACI,iBAAiB;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACrBhB,OAAA,CAACR,IAAI;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACRhB,OAAA,CAACP,QAAQ;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEZhB,OAAA,CAACN,QAAQ;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACZhB,OAAA,CAACL,QAAQ;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEnB;AAACT,EAAA,CA1BQD,GAAG;AAAAa,GAAA,GAAHb,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAjB,EAAA;AAAAiB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}