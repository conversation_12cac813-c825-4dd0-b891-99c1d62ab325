{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Content.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport Button from '../common/Button';\nimport ClientLogos from './ClientLogos';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n  text-align: left;\n  flex: 1;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: 1rem;\n  }\n`;\n_c = Container;\nconst Heading = styled.h1`\n  font-size: 5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 0.5rem;\n  line-height: 1.1;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 3rem;\n  }\n`;\n_c2 = Heading;\nconst SubHeading = styled.h1`\n  font-size: 5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1.5rem;\n  line-height: 1.1;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 3rem;\n  }\n`;\n_c3 = SubHeading;\nconst ColoredText = styled.span`\n  color: ${theme.colors.secondary};\n`;\n_c4 = ColoredText;\nconst Subheading = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  margin-bottom: 3rem;\n  max-width: 700px;\n  line-height: 1.6;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 1rem;\n  }\n`;\n_c5 = Subheading;\nconst FeatureList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  margin-bottom: 3rem;\n`;\n_c6 = FeatureList;\nconst FeatureItem = styled.div`\n  display: flex;\n  align-items: center;\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  \n  &::before {\n    content: '→';\n    margin-right: 1rem;\n    color: ${theme.colors.secondary};\n  }\n`;\n_c7 = FeatureItem;\nconst ButtonContainer = styled.div`\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    flex-direction: column;\n  }\n`;\n_c8 = ButtonContainer;\nconst StyledButton = styled(Button)`\n  border-radius: 9999px;\n  padding: 0.75rem 2rem;\n  font-weight: 600;\n`;\nconst SignUpButton = styled(StyledButton)`\n  background-color: ${theme.colors.secondary};\n  &:hover {\n    background-color: #7c61ff;\n  }\n`;\n_c9 = SignUpButton;\nconst ContactSalesButton = styled(StyledButton)`\n  background-color: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n_c0 = ContactSalesButton;\nconst HeroContent = () => {\n  return /*#__PURE__*/_jsxDEV(Container, {\n    children: [/*#__PURE__*/_jsxDEV(Heading, {\n      children: \"Full-Stack\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubHeading, {\n      children: [\"Infrastructure for \", /*#__PURE__*/_jsxDEV(ColoredText, {\n        children: \"Web3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 28\n      }, this), \" Pros\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Subheading, {\n      children: \"Accelerate your on-chain velocity by adopting the most advanced, full-stack development platform for Web3.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeatureList, {\n      children: [/*#__PURE__*/_jsxDEV(FeatureItem, {\n        children: \"Customizable Node RPC\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeatureItem, {\n        children: \"Collaborative dev infrastructure\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeatureItem, {\n        children: \"Industry-recognized exploration tools\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ButtonContainer, {\n      children: [/*#__PURE__*/_jsxDEV(SignUpButton, {\n        children: \"Sign up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ContactSalesButton, {\n        children: \"Contact sales\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ClientLogos, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_c1 = HeroContent;\nexport default HeroContent;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Heading\");\n$RefreshReg$(_c3, \"SubHeading\");\n$RefreshReg$(_c4, \"ColoredText\");\n$RefreshReg$(_c5, \"Subheading\");\n$RefreshReg$(_c6, \"FeatureList\");\n$RefreshReg$(_c7, \"FeatureItem\");\n$RefreshReg$(_c8, \"ButtonContainer\");\n$RefreshReg$(_c9, \"SignUpButton\");\n$RefreshReg$(_c0, \"ContactSalesButton\");\n$RefreshReg$(_c1, \"HeroContent\");", "map": {"version": 3, "names": ["React", "styled", "theme", "<PERSON><PERSON>", "Client<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Container", "div", "breakpoints", "md", "_c", "Heading", "h1", "colors", "text", "_c2", "SubHeading", "_c3", "ColoredText", "span", "secondary", "_c4", "Subheading", "p", "textSecondary", "_c5", "FeatureList", "_c6", "FeatureItem", "_c7", "ButtonContainer", "sm", "_c8", "StyledButton", "SignUpButton", "_c9", "ContactSalesButton", "_c0", "Hero<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c1", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Content.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport Button from '../common/Button';\nimport ClientLogos from './ClientLogos';\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n  text-align: left;\n  flex: 1;\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 2rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    padding: 1rem;\n  }\n`;\n\nconst Heading = styled.h1`\n  font-size: 5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 0.5rem;\n  line-height: 1.1;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 3rem;\n  }\n`;\n\nconst SubHeading = styled.h1`\n  font-size: 5rem;\n  font-weight: 700;\n  color: ${theme.colors.text};\n  margin-bottom: 1.5rem;\n  line-height: 1.1;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 3rem;\n  }\n`;\n\nconst ColoredText = styled.span`\n  color: ${theme.colors.secondary};\n`;\n\nconst Subheading = styled.p`\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  margin-bottom: 3rem;\n  max-width: 700px;\n  line-height: 1.6;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    font-size: 1rem;\n  }\n`;\n\nconst FeatureList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  margin-bottom: 3rem;\n`;\n\nconst FeatureItem = styled.div`\n  display: flex;\n  align-items: center;\n  font-size: 1.1rem;\n  color: ${theme.colors.textSecondary};\n  \n  &::before {\n    content: '→';\n    margin-right: 1rem;\n    color: ${theme.colors.secondary};\n  }\n`;\n\nconst ButtonContainer = styled.div`\n  display: flex;\n  gap: 1.5rem;\n  margin-bottom: 4rem;\n  \n  @media (max-width: ${theme.breakpoints.sm}) {\n    flex-direction: column;\n  }\n`;\n\nconst StyledButton = styled(Button)`\n  border-radius: 9999px;\n  padding: 0.75rem 2rem;\n  font-weight: 600;\n`;\n\nconst SignUpButton = styled(StyledButton)`\n  background-color: ${theme.colors.secondary};\n  &:hover {\n    background-color: #7c61ff;\n  }\n`;\n\nconst ContactSalesButton = styled(StyledButton)`\n  background-color: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n`;\n\nconst HeroContent: React.FC = () => {\n  return (\n    <Container>\n      <Heading>\n        Full-Stack\n      </Heading>\n      <SubHeading>\n        Infrastructure for <ColoredText>Web3</ColoredText> Pros\n      </SubHeading>\n      <Subheading>\n        Accelerate your on-chain velocity by adopting the most advanced, full-stack development platform for Web3.\n      </Subheading>\n      \n      <FeatureList>\n        <FeatureItem>Customizable Node RPC</FeatureItem>\n        <FeatureItem>Collaborative dev infrastructure</FeatureItem>\n        <FeatureItem>Industry-recognized exploration tools</FeatureItem>\n      </FeatureList>\n      \n      <ButtonContainer>\n        <SignUpButton>Sign up</SignUpButton>\n        <ContactSalesButton>Contact sales</ContactSalesButton>\n      </ButtonContainer>\n      \n      <ClientLogos />\n    </Container>\n  );\n};\n\nexport default HeroContent; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,WAAW,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,SAAS,GAAGN,MAAM,CAACO,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuBN,KAAK,CAACO,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,EAAA,GAdIJ,SAAS;AAgBf,MAAMK,OAAO,GAAGX,MAAM,CAACY,EAAE;AACzB;AACA;AACA,WAAWX,KAAK,CAACY,MAAM,CAACC,IAAI;AAC5B;AACA;AACA;AACA,uBAAuBb,KAAK,CAACO,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACM,GAAA,GAVIJ,OAAO;AAYb,MAAMK,UAAU,GAAGhB,MAAM,CAACY,EAAE;AAC5B;AACA;AACA,WAAWX,KAAK,CAACY,MAAM,CAACC,IAAI;AAC5B;AACA;AACA;AACA,uBAAuBb,KAAK,CAACO,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACQ,GAAA,GAVID,UAAU;AAYhB,MAAME,WAAW,GAAGlB,MAAM,CAACmB,IAAI;AAC/B,WAAWlB,KAAK,CAACY,MAAM,CAACO,SAAS;AACjC,CAAC;AAACC,GAAA,GAFIH,WAAW;AAIjB,MAAMI,UAAU,GAAGtB,MAAM,CAACuB,CAAC;AAC3B;AACA,WAAWtB,KAAK,CAACY,MAAM,CAACW,aAAa;AACrC;AACA;AACA;AACA;AACA,uBAAuBvB,KAAK,CAACO,WAAW,CAACC,EAAE;AAC3C;AACA;AACA,CAAC;AAACgB,GAAA,GAVIH,UAAU;AAYhB,MAAMI,WAAW,GAAG1B,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GALID,WAAW;AAOjB,MAAME,WAAW,GAAG5B,MAAM,CAACO,GAAG;AAC9B;AACA;AACA;AACA,WAAWN,KAAK,CAACY,MAAM,CAACW,aAAa;AACrC;AACA;AACA;AACA;AACA,aAAavB,KAAK,CAACY,MAAM,CAACO,SAAS;AACnC;AACA,CAAC;AAACS,GAAA,GAXID,WAAW;AAajB,MAAME,eAAe,GAAG9B,MAAM,CAACO,GAAG;AAClC;AACA;AACA;AACA;AACA,uBAAuBN,KAAK,CAACO,WAAW,CAACuB,EAAE;AAC3C;AACA;AACA,CAAC;AAACC,GAAA,GARIF,eAAe;AAUrB,MAAMG,YAAY,GAAGjC,MAAM,CAACE,MAAM,CAAC;AACnC;AACA;AACA;AACA,CAAC;AAED,MAAMgC,YAAY,GAAGlC,MAAM,CAACiC,YAAY,CAAC;AACzC,sBAAsBhC,KAAK,CAACY,MAAM,CAACO,SAAS;AAC5C;AACA;AACA;AACA,CAAC;AAACe,GAAA,GALID,YAAY;AAOlB,MAAME,kBAAkB,GAAGpC,MAAM,CAACiC,YAAY,CAAC;AAC/C;AACA;AACA;AACA,CAAC;AAACI,GAAA,GAJID,kBAAkB;AAMxB,MAAME,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACEjC,OAAA,CAACC,SAAS;IAAAiC,QAAA,gBACRlC,OAAA,CAACM,OAAO;MAAA4B,QAAA,EAAC;IAET;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAS,CAAC,eACVtC,OAAA,CAACW,UAAU;MAAAuB,QAAA,GAAC,qBACS,eAAAlC,OAAA,CAACa,WAAW;QAAAqB,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,SACpD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbtC,OAAA,CAACiB,UAAU;MAAAiB,QAAA,EAAC;IAEZ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbtC,OAAA,CAACqB,WAAW;MAAAa,QAAA,gBACVlC,OAAA,CAACuB,WAAW;QAAAW,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChDtC,OAAA,CAACuB,WAAW;QAAAW,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3DtC,OAAA,CAACuB,WAAW;QAAAW,QAAA,EAAC;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC,eAEdtC,OAAA,CAACyB,eAAe;MAAAS,QAAA,gBACdlC,OAAA,CAAC6B,YAAY;QAAAK,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACpCtC,OAAA,CAAC+B,kBAAkB;QAAAG,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAoB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAElBtC,OAAA,CAACF,WAAW;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEhB,CAAC;AAACC,GAAA,GA3BIN,WAAqB;AA6B3B,eAAeA,WAAW;AAAC,IAAA5B,EAAA,EAAAK,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAAnC,EAAA;AAAAmC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAV,GAAA;AAAAU,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}