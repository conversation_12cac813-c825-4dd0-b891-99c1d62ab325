{"ast": null, "code": "import { createGlobalStyle } from 'styled-components';\nimport { theme } from './styles/theme';\nconst GlobalStyles = createGlobalStyle`\n  * {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n  }\n\n  html {\n    font-size: 16px;\n    scroll-behavior: smooth;\n  }\n\n  body {\n    font-family: ${theme.fonts.primary};\n    background-color: ${theme.colors.background};\n    color: ${theme.colors.text};\n    line-height: 1.5;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    overflow-x: hidden;\n    \n    /* Scroll snapping container */\n    scroll-behavior: smooth;\n    &.scroll-snap {\n      scroll-snap-type: y mandatory;\n      scroll-padding-top: 80px; /* Adjust for any fixed headers */\n    }\n  }\n\n  a {\n    color: ${theme.colors.primary};\n    text-decoration: none;\n    transition: color ${theme.transitions.default};\n\n    &:hover {\n      color: ${theme.colors.primaryHover};\n    }\n  }\n\n  img {\n    max-width: 100%;\n    height: auto;\n  }\n\n  button {\n    cursor: pointer;\n    font-family: ${theme.fonts.primary};\n  }\n\n  h1, h2, h3, h4, h5, h6 {\n    margin: 0 0 1rem 0;\n    font-weight: 700;\n    line-height: 1.2;\n  }\n\n  p {\n    margin: 0 0 1rem 0;\n  }\n\n  section {\n    position: relative;\n    padding: 4rem 0;\n  }\n  \n  /* Classes for scrolljacking */\n  .scroll-section {\n    scroll-snap-align: start;\n    scroll-snap-stop: always;\n    min-height: 100vh;\n  }\n`;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["createGlobalStyle", "theme", "GlobalStyles", "fonts", "primary", "colors", "background", "text", "transitions", "default", "primaryHover"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/GlobalStyles.tsx"], "sourcesContent": ["import { createGlobalStyle } from 'styled-components';\nimport { theme } from './styles/theme';\n\nconst GlobalStyles = createGlobalStyle`\n  * {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n  }\n\n  html {\n    font-size: 16px;\n    scroll-behavior: smooth;\n  }\n\n  body {\n    font-family: ${theme.fonts.primary};\n    background-color: ${theme.colors.background};\n    color: ${theme.colors.text};\n    line-height: 1.5;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    overflow-x: hidden;\n    \n    /* Scroll snapping container */\n    scroll-behavior: smooth;\n    &.scroll-snap {\n      scroll-snap-type: y mandatory;\n      scroll-padding-top: 80px; /* Adjust for any fixed headers */\n    }\n  }\n\n  a {\n    color: ${theme.colors.primary};\n    text-decoration: none;\n    transition: color ${theme.transitions.default};\n\n    &:hover {\n      color: ${theme.colors.primaryHover};\n    }\n  }\n\n  img {\n    max-width: 100%;\n    height: auto;\n  }\n\n  button {\n    cursor: pointer;\n    font-family: ${theme.fonts.primary};\n  }\n\n  h1, h2, h3, h4, h5, h6 {\n    margin: 0 0 1rem 0;\n    font-weight: 700;\n    line-height: 1.2;\n  }\n\n  p {\n    margin: 0 0 1rem 0;\n  }\n\n  section {\n    position: relative;\n    padding: 4rem 0;\n  }\n  \n  /* Classes for scrolljacking */\n  .scroll-section {\n    scroll-snap-align: start;\n    scroll-snap-stop: always;\n    min-height: 100vh;\n  }\n`;\n\nexport default GlobalStyles; "], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,KAAK,QAAQ,gBAAgB;AAEtC,MAAMC,YAAY,GAAGF,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBC,KAAK,CAACE,KAAK,CAACC,OAAO;AACtC,wBAAwBH,KAAK,CAACI,MAAM,CAACC,UAAU;AAC/C,aAAaL,KAAK,CAACI,MAAM,CAACE,IAAI;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAaN,KAAK,CAACI,MAAM,CAACD,OAAO;AACjC;AACA,wBAAwBH,KAAK,CAACO,WAAW,CAACC,OAAO;AACjD;AACA;AACA,eAAeR,KAAK,CAACI,MAAM,CAACK,YAAY;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmBT,KAAK,CAACE,KAAK,CAACC,OAAO;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}