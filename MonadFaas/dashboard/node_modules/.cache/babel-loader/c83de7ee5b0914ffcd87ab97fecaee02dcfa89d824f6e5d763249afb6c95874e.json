{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/index.tsx\";\nimport React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport BenefitItem from './BenefitItem';\nimport SectionHeader from './SectionHeader';\nimport BackgroundEffect from './BackgroundEffect';\n\n// Images\nimport benefit1 from '../../assets/home-benefits-1.webp';\nimport benefit2 from '../../assets/home-benefits-2.webp';\nimport benefit3 from '../../assets/home-benefits-3.webp';\nimport benefit4 from '../../assets/home-benefits-4.webp';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BenefitsSection = styled.section`\n  width: 100%;\n  padding: 6rem 0;\n  background-color: ${theme.colors.backgroundDark};\n  position: relative;\n  overflow: hidden;\n`;\n_c = BenefitsSection;\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  position: relative;\n  z-index: 2;\n`;\n_c2 = Container;\nconst BenefitsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-gap: 3rem;\n  margin-top: 5rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n    grid-gap: 2rem;\n  }\n`;\n_c3 = BenefitsGrid;\nconst GridBackground = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: linear-gradient(rgba(0, 210, 194, 0.05) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(0, 210, 194, 0.05) 1px, transparent 1px);\n  background-size: 50px 50px;\n  opacity: 0.5;\n  z-index: 0;\n`;\n\n// Data for benefits items\n_c4 = GridBackground;\nconst benefitsData = [{\n  id: 1,\n  title: 'Local dev',\n  description: 'Integrate Virtual TestNets with Hardhat or Foundry to build with mainnet data locally. Extend your local setup with advanced development tools and facilitate collaboration over a shared infrastructure.',\n  image: benefit1\n}, {\n  id: 2,\n  title: 'CI/CD',\n  description: 'Scale your CI process with managed environments for fast, iterative testing. Speed up your build and release cycles without additional infrastructure management overhead.',\n  image: benefit2\n}, {\n  id: 3,\n  title: 'Dapp staging',\n  description: 'Spin up collaborative staging environments for your smart contract, frontend, and backend teams. Eliminate development silos and enable fast, iterative, and incremental dapp development.',\n  image: benefit3\n}, {\n  id: 4,\n  title: 'Public testing',\n  description: 'Gather real-time feedback and identify actual usage patterns through public dapp testing. Run your dapp on a long-running, publicly accessible Virtual TestNet so users can test your dapp with an unlimited faucet.',\n  image: benefit4\n}];\nconst Benefits = () => {\n  return /*#__PURE__*/_jsxDEV(BenefitsSection, {\n    children: [/*#__PURE__*/_jsxDEV(GridBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(BackgroundEffect, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(SectionHeader, {\n        title: \"Virtual TestNets, development workflows\",\n        subtitle: \"Streamline your development process with purpose-built environments for every stage\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BenefitsGrid, {\n        children: benefitsData.map(benefit => /*#__PURE__*/_jsxDEV(BenefitItem, {\n          title: benefit.title,\n          description: benefit.description,\n          image: benefit.image\n        }, benefit.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_c5 = Benefits;\nexport default Benefits;\nvar _c, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"BenefitsSection\");\n$RefreshReg$(_c2, \"Container\");\n$RefreshReg$(_c3, \"BenefitsGrid\");\n$RefreshReg$(_c4, \"GridBackground\");\n$RefreshReg$(_c5, \"Benefits\");", "map": {"version": 3, "names": ["React", "styled", "theme", "BenefitItem", "SectionHeader", "BackgroundEffect", "benefit1", "benefit2", "benefit3", "benefit4", "jsxDEV", "_jsxDEV", "BenefitsSection", "section", "colors", "backgroundDark", "_c", "Container", "div", "_c2", "BenefitsGrid", "breakpoints", "md", "_c3", "GridBackground", "_c4", "benefitsData", "id", "title", "description", "image", "Benefits", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subtitle", "map", "benefit", "_c5", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/index.tsx"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { theme } from '../../styles/theme';\nimport BenefitItem from './BenefitItem';\nimport SectionHeader from './SectionHeader';\nimport BackgroundEffect from './BackgroundEffect';\n\n// Images\nimport benefit1 from '../../assets/home-benefits-1.webp';\nimport benefit2 from '../../assets/home-benefits-2.webp';\nimport benefit3 from '../../assets/home-benefits-3.webp';\nimport benefit4 from '../../assets/home-benefits-4.webp';\n\nconst BenefitsSection = styled.section`\n  width: 100%;\n  padding: 6rem 0;\n  background-color: ${theme.colors.backgroundDark};\n  position: relative;\n  overflow: hidden;\n`;\n\nconst Container = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 0 2rem;\n  position: relative;\n  z-index: 2;\n`;\n\nconst BenefitsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  grid-gap: 3rem;\n  margin-top: 5rem;\n  \n  @media (max-width: ${theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n    grid-gap: 2rem;\n  }\n`;\n\nconst GridBackground = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-image: linear-gradient(rgba(0, 210, 194, 0.05) 1px, transparent 1px),\n                    linear-gradient(90deg, rgba(0, 210, 194, 0.05) 1px, transparent 1px);\n  background-size: 50px 50px;\n  opacity: 0.5;\n  z-index: 0;\n`;\n\n// Data for benefits items\nconst benefitsData = [\n  {\n    id: 1,\n    title: 'Local dev',\n    description: 'Integrate Virtual TestNets with Hardhat or Foundry to build with mainnet data locally. Extend your local setup with advanced development tools and facilitate collaboration over a shared infrastructure.',\n    image: benefit1,\n  },\n  {\n    id: 2,\n    title: 'CI/CD',\n    description: 'Scale your CI process with managed environments for fast, iterative testing. Speed up your build and release cycles without additional infrastructure management overhead.',\n    image: benefit2,\n  },\n  {\n    id: 3,\n    title: 'Dapp staging',\n    description: 'Spin up collaborative staging environments for your smart contract, frontend, and backend teams. Eliminate development silos and enable fast, iterative, and incremental dapp development.',\n    image: benefit3,\n  },\n  {\n    id: 4,\n    title: 'Public testing',\n    description: 'Gather real-time feedback and identify actual usage patterns through public dapp testing. Run your dapp on a long-running, publicly accessible Virtual TestNet so users can test your dapp with an unlimited faucet.',\n    image: benefit4,\n  },\n];\n\nconst Benefits: React.FC = () => {\n  return (\n    <BenefitsSection>\n      <GridBackground />\n      <BackgroundEffect />\n      <Container>\n        <SectionHeader \n          title=\"Virtual TestNets, development workflows\" \n          subtitle=\"Streamline your development process with purpose-built environments for every stage\"\n        />\n        <BenefitsGrid>\n          {benefitsData.map((benefit) => (\n            <BenefitItem\n              key={benefit.id}\n              title={benefit.title}\n              description={benefit.description}\n              image={benefit.image}\n            />\n          ))}\n        </BenefitsGrid>\n      </Container>\n    </BenefitsSection>\n  );\n};\n\nexport default Benefits; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,KAAK,QAAQ,oBAAoB;AAC1C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;;AAEjD;AACA,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,OAAOC,QAAQ,MAAM,mCAAmC;AACxD,OAAOC,QAAQ,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,eAAe,GAAGX,MAAM,CAACY,OAAO;AACtC;AACA;AACA,sBAAsBX,KAAK,CAACY,MAAM,CAACC,cAAc;AACjD;AACA;AACA,CAAC;AAACC,EAAA,GANIJ,eAAe;AAQrB,MAAMK,SAAS,GAAGhB,MAAM,CAACiB,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,SAAS;AAQf,MAAMG,YAAY,GAAGnB,MAAM,CAACiB,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,uBAAuBhB,KAAK,CAACmB,WAAW,CAACC,EAAE;AAC3C;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAVIH,YAAY;AAYlB,MAAMI,cAAc,GAAGvB,MAAM,CAACiB,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AAAAO,GAAA,GAbMD,cAAc;AAcpB,MAAME,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,2MAA2M;EACxNC,KAAK,EAAExB;AACT,CAAC,EACD;EACEqB,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,OAAO;EACdC,WAAW,EAAE,4KAA4K;EACzLC,KAAK,EAAEvB;AACT,CAAC,EACD;EACEoB,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,cAAc;EACrBC,WAAW,EAAE,4LAA4L;EACzMC,KAAK,EAAEtB;AACT,CAAC,EACD;EACEmB,EAAE,EAAE,CAAC;EACLC,KAAK,EAAE,gBAAgB;EACvBC,WAAW,EAAE,sNAAsN;EACnOC,KAAK,EAAErB;AACT,CAAC,CACF;AAED,MAAMsB,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACEpB,OAAA,CAACC,eAAe;IAAAoB,QAAA,gBACdrB,OAAA,CAACa,cAAc;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBzB,OAAA,CAACN,gBAAgB;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpBzB,OAAA,CAACM,SAAS;MAAAe,QAAA,gBACRrB,OAAA,CAACP,aAAa;QACZwB,KAAK,EAAC,yCAAyC;QAC/CS,QAAQ,EAAC;MAAqF;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC,eACFzB,OAAA,CAACS,YAAY;QAAAY,QAAA,EACVN,YAAY,CAACY,GAAG,CAAEC,OAAO,iBACxB5B,OAAA,CAACR,WAAW;UAEVyB,KAAK,EAAEW,OAAO,CAACX,KAAM;UACrBC,WAAW,EAAEU,OAAO,CAACV,WAAY;UACjCC,KAAK,EAAES,OAAO,CAACT;QAAM,GAHhBS,OAAO,CAACZ,EAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIhB,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEtB,CAAC;AAACI,GAAA,GAvBIT,QAAkB;AAyBxB,eAAeA,QAAQ;AAAC,IAAAf,EAAA,EAAAG,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAe,GAAA;AAAAC,YAAA,CAAAzB,EAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}