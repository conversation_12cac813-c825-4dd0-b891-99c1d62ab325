{"ast": null, "code": "export const theme = {\n  colors: {\n    primary: '#5c45ff',\n    primaryHover: '#4935ff',\n    secondary: '#8a70ff',\n    text: '#ffffff',\n    textSecondary: '#9f99b3',\n    background: '#0e0e1c',\n    backgroundLight: '#1a1a2e',\n    backgroundMedium: '#2a2a42',\n    backgroundDark: '#0a0a14'\n  },\n  fonts: {\n    primary: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif\"\n  },\n  borderRadius: {\n    small: '5px',\n    medium: '10px',\n    large: '15px',\n    pill: '2rem'\n  },\n  spacing: {\n    xs: '0.5rem',\n    sm: '1rem',\n    md: '1.5rem',\n    lg: '2rem',\n    xl: '3rem',\n    xxl: '5rem'\n  },\n  transitions: {\n    default: '0.2s ease'\n  },\n  breakpoints: {\n    xs: '480px',\n    sm: '768px',\n    md: '992px',\n    lg: '1200px',\n    xl: '1440px'\n  }\n};", "map": {"version": 3, "names": ["theme", "colors", "primary", "primaryHover", "secondary", "text", "textSecondary", "background", "backgroundLight", "backgroundMedium", "backgroundDark", "fonts", "borderRadius", "small", "medium", "large", "pill", "spacing", "xs", "sm", "md", "lg", "xl", "xxl", "transitions", "default", "breakpoints"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/styles/theme.ts"], "sourcesContent": ["export const theme = {\n  colors: {\n    primary: '#5c45ff',\n    primaryHover: '#4935ff',\n    secondary: '#8a70ff',\n    text: '#ffffff',\n    textSecondary: '#9f99b3',\n    background: '#0e0e1c',\n    backgroundLight: '#1a1a2e',\n    backgroundMedium: '#2a2a42', \n    backgroundDark: '#0a0a14',\n  },\n  fonts: {\n    primary: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif\"\n  },\n  borderRadius: {\n    small: '5px',\n    medium: '10px',\n    large: '15px',\n    pill: '2rem'\n  },\n  spacing: {\n    xs: '0.5rem',\n    sm: '1rem',\n    md: '1.5rem',\n    lg: '2rem',\n    xl: '3rem',\n    xxl: '5rem'\n  },\n  transitions: {\n    default: '0.2s ease'\n  },\n  breakpoints: {\n    xs: '480px',\n    sm: '768px',\n    md: '992px',\n    lg: '1200px',\n    xl: '1440px'\n  }\n}; "], "mappings": "AAAA,OAAO,MAAMA,KAAK,GAAG;EACnBC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,YAAY,EAAE,SAAS;IACvBC,SAAS,EAAE,SAAS;IACpBC,IAAI,EAAE,SAAS;IACfC,aAAa,EAAE,SAAS;IACxBC,UAAU,EAAE,SAAS;IACrBC,eAAe,EAAE,SAAS;IAC1BC,gBAAgB,EAAE,SAAS;IAC3BC,cAAc,EAAE;EAClB,CAAC;EACDC,KAAK,EAAE;IACLT,OAAO,EAAE;EACX,CAAC;EACDU,YAAY,EAAE;IACZC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,IAAI,EAAE;EACR,CAAC;EACDC,OAAO,EAAE;IACPC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE,MAAM;IACVC,EAAE,EAAE,MAAM;IACVC,GAAG,EAAE;EACP,CAAC;EACDC,WAAW,EAAE;IACXC,OAAO,EAAE;EACX,CAAC;EACDC,WAAW,EAAE;IACXR,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,OAAO;IACXC,EAAE,EAAE,QAAQ;IACZC,EAAE,EAAE;EACN;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}