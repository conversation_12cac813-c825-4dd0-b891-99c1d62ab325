{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/EnhancedImage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst float = keyframes`\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\nconst pulse = keyframes`\n  0%, 100% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 1;\n  }\n`;\nconst shimmer = keyframes`\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n`;\nconst ImageContainer = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n`;\n_c = ImageContainer;\nconst StyledImage = styled.img`\n  width: auto;\n  height: 80%;\n  object-fit: contain;\n  z-index: 2;\n  animation: ${float} 6s ease-in-out infinite;\n  transform-origin: center;\n  filter: drop-shadow(0 5px 15px rgba(0, 210, 194, 0.3));\n  transition: transform 0.3s ease, filter 0.3s ease;\n  \n  &:hover {\n    filter: drop-shadow(0 10px 25px rgba(138, 112, 255, 0.5));\n  }\n`;\n_c2 = StyledImage;\nconst ImageGlow = styled.div`\n  position: absolute;\n  width: 80%;\n  height: 80%;\n  border-radius: 50%;\n  background: radial-gradient(ellipse at center, rgba(0, 210, 194, 0.2) 0%, transparent 70%);\n  filter: blur(20px);\n  z-index: 1;\n  animation: ${pulse} 4s ease-in-out infinite;\n  pointer-events: none;\n`;\n_c3 = ImageGlow;\nconst Shimmer = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    rgba(255, 255, 255, 0) 0%,\n    rgba(255, 255, 255, 0.05) 25%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0.05) 75%,\n    rgba(255, 255, 255, 0) 100%\n  );\n  background-size: 200% 100%;\n  animation: ${shimmer} 8s infinite linear;\n  pointer-events: none;\n  z-index: 3;\n`;\n_c4 = Shimmer;\nconst LoadingPlaceholder = styled.div`\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(110deg, #0c0c14 30%, #151529 50%, #0c0c14 70%);\n  background-size: 200% 100%;\n  animation: ${shimmer} 1.5s infinite linear;\n  border-radius: 8px;\n`;\n_c5 = LoadingPlaceholder;\nconst EnhancedImage = ({\n  src,\n  alt,\n  className\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n  useEffect(() => {\n    // Simple lazy loading simulation\n    const timer = setTimeout(() => {\n      setIsVisible(true);\n    }, 100);\n    return () => clearTimeout(timer);\n  }, []);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n  return /*#__PURE__*/_jsxDEV(ImageContainer, {\n    className: className,\n    children: isVisible && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [!imageLoaded && /*#__PURE__*/_jsxDEV(LoadingPlaceholder, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 28\n      }, this), /*#__PURE__*/_jsxDEV(StyledImage, {\n        src: src,\n        alt: alt,\n        onLoad: handleImageLoad,\n        style: {\n          opacity: imageLoaded ? 1 : 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), imageLoaded && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ImageGlow, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Shimmer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedImage, \"EUYgTgiduJZ8uQPiypRf721mF8Y=\");\n_c6 = EnhancedImage;\nexport default EnhancedImage;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"ImageContainer\");\n$RefreshReg$(_c2, \"StyledImage\");\n$RefreshReg$(_c3, \"ImageGlow\");\n$RefreshReg$(_c4, \"Shimmer\");\n$RefreshReg$(_c5, \"LoadingPlaceholder\");\n$RefreshReg$(_c6, \"EnhancedImage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "styled", "keyframes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "float", "pulse", "shimmer", "ImageContainer", "div", "_c", "StyledImage", "img", "_c2", "ImageGlow", "_c3", "Shimmer", "_c4", "LoadingPlaceholder", "_c5", "EnhancedImage", "src", "alt", "className", "_s", "imageLoaded", "setImageLoaded", "isVisible", "setIsVisible", "timer", "setTimeout", "clearTimeout", "handleImageLoad", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onLoad", "style", "opacity", "_c6", "$RefreshReg$"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/EnhancedImage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport styled, { keyframes } from 'styled-components';\nimport { theme } from '../../styles/theme';\n\ninterface EnhancedImageProps {\n  src: string;\n  alt: string;\n  className?: string;\n}\n\nconst float = keyframes`\n  0%, 100% {\n    transform: translateY(0);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n`;\n\nconst pulse = keyframes`\n  0%, 100% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 1;\n  }\n`;\n\nconst shimmer = keyframes`\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n`;\n\nconst ImageContainer = styled.div`\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n`;\n\nconst StyledImage = styled.img`\n  width: auto;\n  height: 80%;\n  object-fit: contain;\n  z-index: 2;\n  animation: ${float} 6s ease-in-out infinite;\n  transform-origin: center;\n  filter: drop-shadow(0 5px 15px rgba(0, 210, 194, 0.3));\n  transition: transform 0.3s ease, filter 0.3s ease;\n  \n  &:hover {\n    filter: drop-shadow(0 10px 25px rgba(138, 112, 255, 0.5));\n  }\n`;\n\nconst ImageGlow = styled.div`\n  position: absolute;\n  width: 80%;\n  height: 80%;\n  border-radius: 50%;\n  background: radial-gradient(ellipse at center, rgba(0, 210, 194, 0.2) 0%, transparent 70%);\n  filter: blur(20px);\n  z-index: 1;\n  animation: ${pulse} 4s ease-in-out infinite;\n  pointer-events: none;\n`;\n\nconst Shimmer = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    rgba(255, 255, 255, 0) 0%,\n    rgba(255, 255, 255, 0.05) 25%,\n    rgba(255, 255, 255, 0.1) 50%,\n    rgba(255, 255, 255, 0.05) 75%,\n    rgba(255, 255, 255, 0) 100%\n  );\n  background-size: 200% 100%;\n  animation: ${shimmer} 8s infinite linear;\n  pointer-events: none;\n  z-index: 3;\n`;\n\nconst LoadingPlaceholder = styled.div`\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(110deg, #0c0c14 30%, #151529 50%, #0c0c14 70%);\n  background-size: 200% 100%;\n  animation: ${shimmer} 1.5s infinite linear;\n  border-radius: 8px;\n`;\n\nconst EnhancedImage: React.FC<EnhancedImageProps> = ({ src, alt, className }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // Simple lazy loading simulation\n    const timer = setTimeout(() => {\n      setIsVisible(true);\n    }, 100);\n    \n    return () => clearTimeout(timer);\n  }, []);\n  \n  const handleImageLoad = () => {\n    setImageLoaded(true);\n  };\n\n  return (\n    <ImageContainer className={className}>\n      {isVisible && (\n        <>\n          {!imageLoaded && <LoadingPlaceholder />}\n          <StyledImage \n            src={src} \n            alt={alt} \n            onLoad={handleImageLoad} \n            style={{ opacity: imageLoaded ? 1 : 0 }}\n          />\n          {imageLoaded && (\n            <>\n              <ImageGlow />\n              <Shimmer />\n            </>\n          )}\n        </>\n      )}\n    </ImageContainer>\n  );\n};\n\nexport default EnhancedImage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,IAAIC,SAAS,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAStD,MAAMC,KAAK,GAAGL,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMM,KAAK,GAAGN,SAAS;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMO,OAAO,GAAGP,SAAS;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMQ,cAAc,GAAGT,MAAM,CAACU,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAPIF,cAAc;AASpB,MAAMG,WAAW,GAAGZ,MAAM,CAACa,GAAG;AAC9B;AACA;AACA;AACA;AACA,eAAeP,KAAK;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACQ,GAAA,GAbIF,WAAW;AAejB,MAAMG,SAAS,GAAGf,MAAM,CAACU,GAAG;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeH,KAAK;AACpB;AACA,CAAC;AAACS,GAAA,GAVID,SAAS;AAYf,MAAME,OAAO,GAAGjB,MAAM,CAACU,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAeF,OAAO;AACtB;AACA;AACA,CAAC;AAACU,GAAA,GAlBID,OAAO;AAoBb,MAAME,kBAAkB,GAAGnB,MAAM,CAACU,GAAG;AACrC;AACA;AACA;AACA;AACA,eAAeF,OAAO;AACtB;AACA,CAAC;AAACY,GAAA,GAPID,kBAAkB;AASxB,MAAME,aAA2C,GAAGA,CAAC;EAAEC,GAAG;EAAEC,GAAG;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd;IACA,MAAM+B,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,YAAY,CAAC,IAAI,CAAC;IACpB,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5BN,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,oBACExB,OAAA,CAACM,cAAc;IAACe,SAAS,EAAEA,SAAU;IAAAU,QAAA,EAClCN,SAAS,iBACRzB,OAAA,CAAAE,SAAA;MAAA6B,QAAA,GACG,CAACR,WAAW,iBAAIvB,OAAA,CAACgB,kBAAkB;QAAAgB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvCnC,OAAA,CAACS,WAAW;QACVU,GAAG,EAAEA,GAAI;QACTC,GAAG,EAAEA,GAAI;QACTgB,MAAM,EAAEN,eAAgB;QACxBO,KAAK,EAAE;UAAEC,OAAO,EAAEf,WAAW,GAAG,CAAC,GAAG;QAAE;MAAE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC,EACDZ,WAAW,iBACVvB,OAAA,CAAAE,SAAA;QAAA6B,QAAA,gBACE/B,OAAA,CAACY,SAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACbnC,OAAA,CAACc,OAAO;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eACX,CACH;IAAA,eACD;EACH;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAACb,EAAA,CAtCIJ,aAA2C;AAAAqB,GAAA,GAA3CrB,aAA2C;AAwCjD,eAAeA,aAAa;AAAC,IAAAV,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAsB,GAAA;AAAAC,YAAA,CAAAhC,EAAA;AAAAgC,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}