{"ast": null, "code": "'use strict';\n\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};", "map": {"version": 3, "names": ["DESCRIPTORS", "require", "definePropertyModule", "createPropertyDescriptor", "module", "exports", "object", "key", "value", "f"], "sources": ["/home/<USER>/Desktop/Repos/adiweb/node_modules/core-js-pure/internals/create-non-enumerable-property.js"], "sourcesContent": ["'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACrD,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AACzE,IAAIE,wBAAwB,GAAGF,OAAO,CAAC,yCAAyC,CAAC;AAEjFG,MAAM,CAACC,OAAO,GAAGL,WAAW,GAAG,UAAUM,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC3D,OAAON,oBAAoB,CAACO,CAAC,CAACH,MAAM,EAAEC,GAAG,EAAEJ,wBAAwB,CAAC,CAAC,EAAEK,KAAK,CAAC,CAAC;AAChF,CAAC,GAAG,UAAUF,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAChCF,MAAM,CAACC,GAAG,CAAC,GAAGC,KAAK;EACnB,OAAOF,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}