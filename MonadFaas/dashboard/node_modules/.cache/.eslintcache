[{"/home/<USER>/Desktop/Repos/adiweb/src/index.tsx": "1", "/home/<USER>/Desktop/Repos/adiweb/src/reportWebVitals.ts": "2", "/home/<USER>/Desktop/Repos/adiweb/src/App.tsx": "3", "/home/<USER>/Desktop/Repos/adiweb/src/GlobalStyles.tsx": "4", "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/BackgroundImage.tsx": "5", "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/index.tsx": "6", "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/index.tsx": "7", "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/index.tsx": "8", "/home/<USER>/Desktop/Repos/adiweb/src/components/Explorer/index.tsx": "9", "/home/<USER>/Desktop/Repos/adiweb/src/styles/theme.ts": "10", "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Icons.tsx": "11", "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Nav.tsx": "12", "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Content.tsx": "13", "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/DashboardModel.tsx": "14", "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Planets/Planet.tsx": "15", "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/SectionHeader.tsx": "16", "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BackgroundEffect.tsx": "17", "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BenefitItem.tsx": "18", "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Cards/FeatureCard.tsx": "19", "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/ClientLogos.tsx": "20", "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/EnhancedImage.tsx": "21", "/home/<USER>/Desktop/Repos/adiweb/src/components/common/Button.tsx": "22"}, {"size": 554, "mtime": 1748672868221, "results": "23", "hashOfConfig": "24"}, {"size": 425, "mtime": 1748672868221, "results": "25", "hashOfConfig": "24"}, {"size": 1440, "mtime": 1748672868217, "results": "26", "hashOfConfig": "24"}, {"size": 1428, "mtime": 1748672868217, "results": "27", "hashOfConfig": "24"}, {"size": 3707, "mtime": 1748672868220, "results": "28", "hashOfConfig": "24"}, {"size": 16483, "mtime": 1748672868221, "results": "29", "hashOfConfig": "24"}, {"size": 2356, "mtime": 1748672868221, "results": "30", "hashOfConfig": "24"}, {"size": 3257, "mtime": 1748672868220, "results": "31", "hashOfConfig": "24"}, {"size": 13660, "mtime": 1748672868220, "results": "32", "hashOfConfig": "24"}, {"size": 852, "mtime": 1748672868221, "results": "33", "hashOfConfig": "24"}, {"size": 6072, "mtime": 1748672868220, "results": "34", "hashOfConfig": "24"}, {"size": 3072, "mtime": 1748672868221, "results": "35", "hashOfConfig": "24"}, {"size": 3037, "mtime": 1748672868221, "results": "36", "hashOfConfig": "24"}, {"size": 13663, "mtime": 1748672868221, "results": "37", "hashOfConfig": "24"}, {"size": 10406, "mtime": 1748672868221, "results": "38", "hashOfConfig": "24"}, {"size": 1224, "mtime": 1748672868220, "results": "39", "hashOfConfig": "24"}, {"size": 3856, "mtime": 1748672868220, "results": "40", "hashOfConfig": "24"}, {"size": 2638, "mtime": 1748672868220, "results": "41", "hashOfConfig": "24"}, {"size": 3016, "mtime": 1748672868220, "results": "42", "hashOfConfig": "24"}, {"size": 1390, "mtime": 1748672868221, "results": "43", "hashOfConfig": "24"}, {"size": 3064, "mtime": 1748672868220, "results": "44", "hashOfConfig": "24"}, {"size": 1855, "mtime": 1748672868221, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "u9nlbq", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/Desktop/Repos/adiweb/src/index.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/reportWebVitals.ts", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/App.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/GlobalStyles.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/BackgroundImage.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/index.tsx", ["112", "113"], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/index.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/index.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Explorer/index.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/styles/theme.ts", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Icons.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Nav.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/Content.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/DashboardModel.tsx", ["114"], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Planets/Planet.tsx", ["115"], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/SectionHeader.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BackgroundEffect.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/BenefitItem.tsx", ["116"], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Features/Cards/FeatureCard.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Hero/ClientLogos.tsx", [], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/Benefits/EnhancedImage.tsx", ["117"], [], "/home/<USER>/Desktop/Repos/adiweb/src/components/common/Button.tsx", [], [], {"ruleId": "118", "severity": 1, "message": "119", "line": 17, "column": 7, "nodeType": "120", "messageId": "121", "endLine": 17, "endColumn": 11}, {"ruleId": "118", "severity": 1, "message": "122", "line": 46, "column": 7, "nodeType": "120", "messageId": "121", "endLine": 46, "endColumn": 15}, {"ruleId": "118", "severity": 1, "message": "123", "line": 26, "column": 7, "nodeType": "120", "messageId": "121", "endLine": 26, "endColumn": 20}, {"ruleId": "118", "severity": 1, "message": "124", "line": 1, "column": 17, "nodeType": "120", "messageId": "121", "endLine": 1, "endColumn": 25}, {"ruleId": "118", "severity": 1, "message": "125", "line": 12, "column": 7, "nodeType": "120", "messageId": "121", "endLine": 12, "endColumn": 12}, {"ruleId": "118", "severity": 1, "message": "126", "line": 3, "column": 10, "nodeType": "120", "messageId": "121", "endLine": 3, "endColumn": 15}, "@typescript-eslint/no-unused-vars", "'glow' is assigned a value but never used.", "Identifier", "unusedVar", "'lineGrow' is assigned a value but never used.", "'rotateReverse' is assigned a value but never used.", "'useState' is defined but never used.", "'float' is assigned a value but never used.", "'theme' is defined but never used."]