{"version": 3, "names": ["exports", "initialize", "babel", "init", "version", "traverse", "types", "tokTypes", "parseSync", "loadPartialConfigSync", "loadPartialConfigAsync", "createConfigItemSync", "createConfigItem", "require"], "sources": ["../../src/worker/babel-core.cts"], "sourcesContent": ["export = exports as typeof import(\"@babel/core\") & {\n  init: Promise<void> | null;\n};\n\nfunction initialize(babel: typeof import(\"@babel/core\")) {\n  exports.init = null;\n  exports.version = babel.version;\n  exports.traverse = babel.traverse;\n  exports.types = babel.types;\n  exports.tokTypes = babel.tokTypes;\n  exports.parseSync = babel.parseSync;\n  exports.loadPartialConfigSync = babel.loadPartialConfigSync;\n  exports.loadPartialConfigAsync = babel.loadPartialConfigAsync;\n  if (process.env.BABEL_8_BREAKING) {\n    exports.createConfigItemSync = babel.createConfigItemSync;\n  } else {\n    // babel.createConfigItemSync is available on 7.13+\n    // we support Babel 7.11+\n    exports.createConfigItemSync =\n      babel.createConfigItemSync || babel.createConfigItem;\n  }\n}\n\nif (USE_ESM) {\n  exports.init = import(\"@babel/core\").then(initialize);\n} else {\n  initialize(require(\"@babel/core\"));\n}\n"], "mappings": ";;iBAASA,OAAO;AAIhB,SAASC,UAAUA,CAACC,KAAmC,EAAE;EACvDF,OAAO,CAACG,IAAI,GAAG,IAAI;EACnBH,OAAO,CAACI,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC/BJ,OAAO,CAACK,QAAQ,GAAGH,KAAK,CAACG,QAAQ;EACjCL,OAAO,CAACM,KAAK,GAAGJ,KAAK,CAACI,KAAK;EAC3BN,OAAO,CAACO,QAAQ,GAAGL,KAAK,CAACK,QAAQ;EACjCP,OAAO,CAACQ,SAAS,GAAGN,KAAK,CAACM,SAAS;EACnCR,OAAO,CAACS,qBAAqB,GAAGP,KAAK,CAACO,qBAAqB;EAC3DT,OAAO,CAACU,sBAAsB,GAAGR,KAAK,CAACQ,sBAAsB;EAGtD;IAGLV,OAAO,CAACW,oBAAoB,GAC1BT,KAAK,CAACS,oBAAoB,IAAIT,KAAK,CAACU,gBAAgB;EACxD;AACF;AAIO;EACLX,UAAU,CAACY,OAAO,CAAC,aAAa,CAAC,CAAC;AACpC", "ignoreList": []}