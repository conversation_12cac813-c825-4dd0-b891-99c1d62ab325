import React, { useEffect } from 'react';
import Hero from './components/Hero';
import Features from './components/Features';
import Benefits from './components/Benefits';
import Explorer from './components/Explorer';
import Statistics from './components/Statistics';
import GlobalStyles from './GlobalStyles';
import styled from 'styled-components';
import BackgroundImage from './components/Features/BackgroundImage';

const AppContainer = styled.div`
  position: relative;
  width: 100%;
  min-height: 100vh;
  scroll-behavior: smooth;
`;

const BackgroundPattern = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(rgba(14, 14, 28, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(14, 14, 28, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  z-index: 1;
  pointer-events: none;
`;

function App() {
  // Apply scroll-snap behavior to body when Explorer section is in view
  useEffect(() => {
    const body = document.body;
    
    // Add CSS for smooth scrolling
    body.style.scrollBehavior = 'smooth';
    
    return () => {
      // Cleanup
      body.style.scrollBehavior = '';
    };
  }, []);
  
  return (
    <AppContainer>
      <GlobalStyles />
      <BackgroundImage src="/features-bg.webp" overlayOpacity={0.9} />
      <BackgroundPattern />
      <Hero />
      <Features />
      <Statistics />
      <Benefits />
      <Explorer />
    </AppContainer>
  );
}

export default App;
