export const theme = {
  colors: {
    primary: '#5c45ff',
    primaryHover: '#4935ff',
    secondary: '#8a70ff',
    text: '#ffffff',
    textSecondary: '#9f99b3',
    background: '#0e0e1c',
    backgroundLight: '#1a1a2e',
    backgroundMedium: '#2a2a42', 
    backgroundDark: '#0a0a14',
  },
  fonts: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif"
  },
  borderRadius: {
    small: '5px',
    medium: '10px',
    large: '15px',
    pill: '2rem'
  },
  spacing: {
    xs: '0.5rem',
    sm: '1rem',
    md: '1.5rem',
    lg: '2rem',
    xl: '3rem',
    xxl: '5rem'
  },
  transitions: {
    default: '0.2s ease'
  },
  breakpoints: {
    xs: '480px',
    sm: '768px',
    md: '992px',
    lg: '1200px',
    xl: '1440px'
  }
}; 