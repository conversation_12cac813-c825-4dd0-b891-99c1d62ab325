{"name": "sax", "description": "An evented streaming XML parser in JavaScript", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "version": "1.2.4", "main": "lib/sax.js", "license": "ISC", "scripts": {"test": "tap test/*.js --cov -j4", "posttest": "standard -F test/*.js lib/*.js", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "repository": "git://github.com/isaacs/sax-js.git", "files": ["lib/sax.js", "LICENSE", "README.md"], "devDependencies": {"standard": "^8.6.0", "tap": "^10.5.1"}}