{"version": 3, "file": "index.spec.js", "sourceRoot": "", "sources": ["../src/index.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,wBAAwB,EAAW,MAAM,GAAG,CAAC;AAElE,IAAM,UAAU,GAAiC;IAC/C,CAAC,EAAE,EAAE,EAAE,CAAC;IACR,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,aAAa,EAAE,YAAY,CAAC;IAC7B,CAAC,aAAa,EAAE,YAAY,CAAC;IAC7B,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IACpC,CAAC,gBAAgB,EAAE,aAAa,EAAE,EAAE,SAAS,EAAE,wBAAwB,EAAE,CAAC;CAC3E,CAAC;AAEF,QAAQ,CAAC,aAAa,EAAE;4BACV,KAAK,EAAE,MAAM,EAAE,OAAO;QAChC,EAAE,CAAI,KAAK,YAAO,MAAQ,EAAE;YAC1B,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;;IAHL,KAAuC,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU;QAAtC,IAAA,qBAAwB,EAAvB,KAAK,QAAA,EAAE,MAAM,QAAA,EAAE,OAAO,QAAA;gBAAtB,KAAK,EAAE,MAAM,EAAE,OAAO;KAIjC;AACH,CAAC,CAAC,CAAC", "sourcesContent": ["import { pascalCase, pascalCaseTransformMerge, Options } from \".\";\n\nconst TEST_CASES: [string, string, Options?][] = [\n  [\"\", \"\"],\n  [\"test\", \"Test\"],\n  [\"test string\", \"TestString\"],\n  [\"Test String\", \"TestString\"],\n  [\"TestV2\", \"TestV2\"],\n  [\"version 1.2.10\", \"Version_1_2_10\"],\n  [\"version 1.21.0\", \"Version_1_21_0\"],\n  [\"version 1.21.0\", \"Version1210\", { transform: pascalCaseTransformMerge }],\n];\n\ndescribe(\"pascal case\", () => {\n  for (const [input, result, options] of TEST_CASES) {\n    it(`${input} -> ${result}`, () => {\n      expect(pascalCase(input, options)).toEqual(result);\n    });\n  }\n});\n"]}