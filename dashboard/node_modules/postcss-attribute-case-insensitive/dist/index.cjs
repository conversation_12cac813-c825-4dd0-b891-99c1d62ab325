"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-selector-parser"));function n(e){return"attribute"===e.type&&e.insensitive}function s(e,t,n){const c=n.charAt(t);if(""===c)return e;let r=e.map((e=>e+c));const o=c.toLocaleUpperCase();return o!==c&&(r=r.concat(e.map((e=>e+o)))),s(r,t+1,n)}function c(e){return s([""],0,e.value).map((t=>{const n=e.clone({spaces:{after:e.spaces.after,before:e.spaces.before},insensitive:!1});return n.setValue(t),n}))}function r(e){let s=[];e.each((e=>{(function(e){return e.some(n)})(e)&&(s=s.concat(function(e){let s=[t.default.selector({value:"",nodes:[]})];return e.walk((e=>{if(!n(e))return void s.forEach((t=>{t.append(e.clone())}));const t=c(e),r=[];t.forEach((e=>{s.forEach((t=>{const n=t.clone({});n.append(e),r.push(n)}))})),s=r})),s}(e)),e.remove())})),s.length&&s.forEach((t=>e.append(t)))}const o=()=>({postcssPlugin:"postcss-attribute-case-insensitive",Rule(e){if(e.selector.includes("i]")){const n=t.default(r).processSync(e.selector);if(n===e.selector)return;e.replaceWith(e.clone({selector:n}))}}});o.postcss=!0,module.exports=o;
