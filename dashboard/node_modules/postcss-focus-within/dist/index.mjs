import e from"postcss-selector-parser";const s=s=>{const t=String(Object(s).replaceWith||"[focus-within]"),r=Boolean(!("preserve"in Object(s))||s.preserve),o=e().astSync(t);return{postcssPlugin:"postcss-focus-within",Rule:(s,{result:t})=>{if(!s.selector.includes(":focus-within"))return;let c;try{const t=e((e=>{e.walkPseudos((e=>{":focus-within"===e.value&&(e.nodes&&e.nodes.length||e.replaceWith(o.clone({})))}))})).processSync(s.selector);c=String(t)}catch(e){return void s.warn(t,`Failed to parse selector : ${s.selector}`)}if(void 0===c)return;if(c===s.selector)return;const n=s.clone({selector:c});r?s.before(n):s.replaceWith(n)}}};s.postcss=!0;export{s as default};
